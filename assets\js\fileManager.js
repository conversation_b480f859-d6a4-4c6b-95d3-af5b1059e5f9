/**
 * مدير الملفات - معالجة وإدارة الملفات المختلفة
 * يدعم TXT, HTML, Excel, CSV
 */

// ===== فئة مدير الملفات =====
class FileManager {
    constructor() {
        this.supportedTypes = {
            text: ['.txt', '.md'],
            html: ['.html', '.htm'],
            excel: ['.xlsx', '.xls'],
            csv: ['.csv']
        };
        this.maxFileSize = 10 * 1024 * 1024; // 10 ميجابايت
        this.maxFiles = 50;
    }

    // ===== قراءة الملف حسب نوعه =====
    async readFile(file) {
        try {
            const extension = this.getFileExtension(file.name);
            
            switch (true) {
                case this.supportedTypes.text.includes(extension):
                    return await this.readTextFile(file);
                
                case this.supportedTypes.html.includes(extension):
                    return await this.readHTMLFile(file);
                
                case this.supportedTypes.excel.includes(extension):
                    return await this.readExcelFile(file);
                
                case this.supportedTypes.csv.includes(extension):
                    return await this.readCSVFile(file);
                
                default:
                    throw new Error(`نوع الملف غير مدعوم: ${extension}`);
            }
        } catch (error) {
            console.error('خطأ في قراءة الملف:', error);
            throw error;
        }
    }

    // ===== قراءة ملف نصي =====
    async readTextFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const content = e.target.result;
                resolve({
                    type: 'text',
                    filename: file.name,
                    content: content,
                    wordCount: TextHelper.countWords(content),
                    charCount: TextHelper.countCharacters(content)
                });
            };
            
            reader.onerror = () => reject(new Error('خطأ في قراءة الملف النصي'));
            reader.readAsText(file, 'UTF-8');
        });
    }

    // ===== قراءة ملف HTML =====
    async readHTMLFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const htmlContent = e.target.result;
                const textContent = TextHelper.extractTextFromHTML(htmlContent);
                
                resolve({
                    type: 'html',
                    filename: file.name,
                    htmlContent: htmlContent,
                    textContent: textContent,
                    wordCount: TextHelper.countWords(textContent),
                    charCount: TextHelper.countCharacters(textContent)
                });
            };
            
            reader.onerror = () => reject(new Error('خطأ في قراءة ملف HTML'));
            reader.readAsText(file, 'UTF-8');
        });
    }

    // ===== قراءة ملف Excel =====
    async readExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = async (e) => {
                try {
                    // استخدام مكتبة SheetJS لقراءة Excel
                    if (typeof XLSX === 'undefined') {
                        // تحميل المكتبة ديناميكياً
                        await this.loadSheetJS();
                    }
                    
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    const articles = [];
                    
                    // قراءة جميع الأوراق
                    workbook.SheetNames.forEach(sheetName => {
                        const worksheet = workbook.Sheets[sheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                        
                        // البحث عن أعمدة العنوان والمحتوى
                        if (jsonData.length > 0) {
                            const headers = jsonData[0];
                            const titleCol = this.findColumn(headers, ['عنوان', 'title', 'العنوان']);
                            const contentCol = this.findColumn(headers, ['محتوى', 'content', 'المحتوى', 'نص']);
                            
                            for (let i = 1; i < jsonData.length; i++) {
                                const row = jsonData[i];
                                if (row && row.length > 0) {
                                    const title = titleCol !== -1 ? row[titleCol] : '';
                                    const content = contentCol !== -1 ? row[contentCol] : row.join(' ');
                                    
                                    if (content && content.trim()) {
                                        articles.push({
                                            title: title || `مقال ${i}`,
                                            content: content.trim(),
                                            sheet: sheetName,
                                            row: i + 1
                                        });
                                    }
                                }
                            }
                        }
                    });
                    
                    resolve({
                        type: 'excel',
                        filename: file.name,
                        articles: articles,
                        totalArticles: articles.length,
                        sheets: workbook.SheetNames
                    });
                    
                } catch (error) {
                    reject(new Error('خطأ في معالجة ملف Excel: ' + error.message));
                }
            };
            
            reader.onerror = () => reject(new Error('خطأ في قراءة ملف Excel'));
            reader.readAsArrayBuffer(file);
        });
    }

    // ===== قراءة ملف CSV =====
    async readCSVFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const csvContent = e.target.result;
                    const lines = csvContent.split('\n');
                    const articles = [];
                    
                    if (lines.length > 1) {
                        const headers = this.parseCSVLine(lines[0]);
                        const titleCol = this.findColumn(headers, ['عنوان', 'title', 'العنوان']);
                        const contentCol = this.findColumn(headers, ['محتوى', 'content', 'المحتوى', 'نص']);
                        
                        for (let i = 1; i < lines.length; i++) {
                            const line = lines[i].trim();
                            if (line) {
                                const columns = this.parseCSVLine(line);
                                const title = titleCol !== -1 ? columns[titleCol] : '';
                                const content = contentCol !== -1 ? columns[contentCol] : columns.join(' ');
                                
                                if (content && content.trim()) {
                                    articles.push({
                                        title: title || `مقال ${i}`,
                                        content: content.trim(),
                                        row: i + 1
                                    });
                                }
                            }
                        }
                    }
                    
                    resolve({
                        type: 'csv',
                        filename: file.name,
                        articles: articles,
                        totalArticles: articles.length
                    });
                    
                } catch (error) {
                    reject(new Error('خطأ في معالجة ملف CSV: ' + error.message));
                }
            };
            
            reader.onerror = () => reject(new Error('خطأ في قراءة ملف CSV'));
            reader.readAsText(file, 'UTF-8');
        });
    }

    // ===== دوال مساعدة =====
    
    getFileExtension(filename) {
        return filename.toLowerCase().substring(filename.lastIndexOf('.'));
    }

    findColumn(headers, searchTerms) {
        for (let i = 0; i < headers.length; i++) {
            const header = headers[i] ? headers[i].toString().toLowerCase() : '';
            for (const term of searchTerms) {
                if (header.includes(term.toLowerCase())) {
                    return i;
                }
            }
        }
        return -1;
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current.trim());
        return result;
    }

    // ===== تحميل مكتبة SheetJS =====
    async loadSheetJS() {
        return new Promise((resolve, reject) => {
            if (typeof XLSX !== 'undefined') {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            script.onload = resolve;
            script.onerror = () => reject(new Error('فشل في تحميل مكتبة Excel'));
            document.head.appendChild(script);
        });
    }

    // ===== التحقق من صحة الملف =====
    validateFile(file) {
        const errors = [];
        
        // التحقق من الحجم
        if (file.size > this.maxFileSize) {
            errors.push(`حجم الملف كبير جداً: ${Formatter.formatFileSize(file.size)}. الحد الأقصى: ${Formatter.formatFileSize(this.maxFileSize)}`);
        }
        
        // التحقق من النوع
        const extension = this.getFileExtension(file.name);
        const allSupportedTypes = [
            ...this.supportedTypes.text,
            ...this.supportedTypes.html,
            ...this.supportedTypes.excel,
            ...this.supportedTypes.csv
        ];
        
        if (!allSupportedTypes.includes(extension)) {
            errors.push(`نوع الملف غير مدعوم: ${extension}`);
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // ===== معالجة متعددة الملفات =====
    async processMultipleFiles(files, onProgress = null) {
        const results = [];
        const errors = [];
        
        for (let i = 0; i < files.length; i++) {
            try {
                // التحقق من صحة الملف
                const validation = this.validateFile(files[i]);
                if (!validation.isValid) {
                    errors.push({
                        filename: files[i].name,
                        errors: validation.errors
                    });
                    continue;
                }
                
                // قراءة الملف
                const result = await this.readFile(files[i]);
                results.push(result);
                
                // تحديث التقدم
                if (onProgress) {
                    onProgress({
                        current: i + 1,
                        total: files.length,
                        percentage: ((i + 1) / files.length) * 100,
                        currentFile: files[i].name
                    });
                }
                
            } catch (error) {
                errors.push({
                    filename: files[i].name,
                    errors: [error.message]
                });
            }
        }
        
        return {
            results: results,
            errors: errors,
            totalProcessed: results.length,
            totalErrors: errors.length
        };
    }

    // ===== تصدير البيانات =====
    exportToText(data, filename) {
        let content = '';
        
        if (Array.isArray(data)) {
            data.forEach((item, index) => {
                content += `=== مقال ${index + 1} ===\n`;
                if (item.title) content += `العنوان: ${item.title}\n\n`;
                content += `${item.content}\n\n`;
                content += '='.repeat(50) + '\n\n';
            });
        } else {
            content = data.content || data.toString();
        }
        
        FileHelper.downloadFile(content, filename, 'text/plain; charset=utf-8');
    }

    exportToHTML(data, filename) {
        let html = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المقالات المحسنة</title>
    <style>
        body { font-family: 'Cairo', 'Tajawal', sans-serif; margin: 20px; }
        .article { margin-bottom: 40px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .article-title { color: #2563eb; margin-bottom: 15px; }
        .article-content { line-height: 1.8; }
    </style>
</head>
<body>
`;
        
        if (Array.isArray(data)) {
            data.forEach((item, index) => {
                html += `
    <div class="article">
        <h2 class="article-title">${item.title || `مقال ${index + 1}`}</h2>
        <div class="article-content">${item.content}</div>
    </div>
`;
            });
        } else {
            html += `
    <div class="article">
        <div class="article-content">${data.content || data}</div>
    </div>
`;
        }
        
        html += `
</body>
</html>`;
        
        FileHelper.downloadFile(html, filename, 'text/html; charset=utf-8');
    }

    // ===== تقسيم المحتوى الكبير =====
    splitLargeContent(content, maxChunkSize = 25000) {
        if (content.length <= maxChunkSize) {
            return [content];
        }
        
        const chunks = [];
        const sentences = content.split(/[.!?]\s+/);
        let currentChunk = '';
        
        for (const sentence of sentences) {
            if ((currentChunk + sentence).length <= maxChunkSize) {
                currentChunk += (currentChunk ? '. ' : '') + sentence;
            } else {
                if (currentChunk) {
                    chunks.push(currentChunk + '.');
                }
                currentChunk = sentence;
            }
        }
        
        if (currentChunk) {
            chunks.push(currentChunk + '.');
        }
        
        return chunks;
    }
}

// ===== إنشاء مثيل عام =====
window.FileManager = new FileManager();

console.log('تم تحميل مدير الملفات بنجاح');
