/**
 * محرك الذكاء الصناعي - التكامل مع Gemini API
 * إدارة الطلبات والتوكنز والتدوير بين المفاتيح
 */

// ===== فئة محرك الذكاء الصناعي =====
class AIEngine {
    constructor() {
        this.config = GEMINI_CONFIG;
        this.requestQueue = [];
        this.isProcessing = false;
        this.currentKeyIndex = 0;
        this.keyUsage = new Map();
        this.rateLimiter = new Map();
        
        // تهيئة إحصائيات المفاتيح
        this.initializeKeyStats();
    }

    // ===== تهيئة إحصائيات المفاتيح =====
    initializeKeyStats() {
        this.config.API_KEYS.forEach((key, index) => {
            this.keyUsage.set(index, {
                requestsToday: 0,
                requestsThisMinute: 0,
                lastResetTime: new Date().toDateString(),
                lastMinuteReset: Date.now(),
                errors: 0,
                isBlocked: false
            });
        });
    }

    // ===== الحصول على المفتاح التالي المتاح =====
    getNextAvailableKey() {
        const now = Date.now();
        const today = new Date().toDateString();
        
        for (let i = 0; i < this.config.API_KEYS.length; i++) {
            const keyIndex = (this.currentKeyIndex + i) % this.config.API_KEYS.length;
            const usage = this.keyUsage.get(keyIndex);
            
            // إعادة تعيين الإحصائيات اليومية
            if (usage.lastResetTime !== today) {
                usage.requestsToday = 0;
                usage.lastResetTime = today;
                usage.errors = 0;
                usage.isBlocked = false;
            }
            
            // إعادة تعيين إحصائيات الدقيقة
            if (now - usage.lastMinuteReset > 60000) {
                usage.requestsThisMinute = 0;
                usage.lastMinuteReset = now;
            }
            
            // التحقق من توفر المفتاح
            if (!usage.isBlocked && 
                usage.requestsToday < this.config.MAX_REQUESTS_PER_DAY &&
                usage.requestsThisMinute < this.config.MAX_REQUESTS_PER_MINUTE) {
                
                this.currentKeyIndex = keyIndex;
                return {
                    key: this.config.API_KEYS[keyIndex],
                    index: keyIndex
                };
            }
        }
        
        throw new Error('جميع مفاتيح API وصلت للحد الأقصى من الطلبات');
    }

    // ===== إرسال طلب إلى Gemini API =====
    async sendRequest(prompt, settings = {}) {
        try {
            const keyInfo = this.getNextAvailableKey();
            const usage = this.keyUsage.get(keyInfo.index);
            
            // بناء رابط الطلب
            const url = `${this.config.BASE_URL}?key=${keyInfo.key}`;
            
            // بناء محتوى الطلب
            const requestBody = {
                contents: [{
                    parts: [{ text: prompt }]
                }],
                generationConfig: {
                    temperature: settings.temperature || 0.7,
                    topK: settings.topK || 40,
                    topP: settings.topP || 0.95,
                    maxOutputTokens: settings.maxTokens || 8192,
                }
            };
            
            // إرسال الطلب
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });
            
            // تحديث الإحصائيات
            usage.requestsToday++;
            usage.requestsThisMinute++;
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`HTTP ${response.status}: ${errorData.error?.message || 'خطأ غير معروف'}`);
            }
            
            const data = await response.json();
            
            // استخراج النص من الاستجابة
            if (data.candidates && data.candidates.length > 0) {
                const content = data.candidates[0].content;
                if (content && content.parts && content.parts.length > 0) {
                    return {
                        success: true,
                        content: content.parts[0].text,
                        keyIndex: keyInfo.index,
                        usage: {
                            promptTokens: data.usageMetadata?.promptTokenCount || 0,
                            completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
                            totalTokens: data.usageMetadata?.totalTokenCount || 0
                        }
                    };
                }
            }
            
            throw new Error('استجابة غير صحيحة من API');
            
        } catch (error) {
            console.error('خطأ في طلب API:', error);
            
            // تسجيل الخطأ
            if (this.currentKeyIndex !== undefined) {
                const usage = this.keyUsage.get(this.currentKeyIndex);
                usage.errors++;
                
                // حظر المفتاح إذا كان هناك أخطاء كثيرة
                if (usage.errors >= 5) {
                    usage.isBlocked = true;
                    console.warn(`تم حظر المفتاح ${this.currentKeyIndex} بسبب كثرة الأخطاء`);
                }
            }
            
            return {
                success: false,
                error: error.message,
                keyIndex: this.currentKeyIndex
            };
        }
    }

    // ===== معالجة طلب مع إعادة المحاولة =====
    async processWithRetry(prompt, settings = {}, maxRetries = 3) {
        let lastError = null;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // انتظار بين المحاولات
                if (attempt > 1) {
                    await this.delay(this.config.RETRY_DELAY * attempt);
                }
                
                const result = await this.sendRequest(prompt, settings);
                
                if (result.success) {
                    return result;
                }
                
                lastError = result.error;
                console.warn(`المحاولة ${attempt} فشلت:`, result.error);
                
                // تجربة مفتاح آخر في المحاولة التالية
                this.currentKeyIndex = (this.currentKeyIndex + 1) % this.config.API_KEYS.length;
                
            } catch (error) {
                lastError = error.message;
                console.error(`خطأ في المحاولة ${attempt}:`, error);
            }
        }
        
        throw new Error(`فشل في جميع المحاولات. آخر خطأ: ${lastError}`);
    }

    // ===== تحسين المقال =====
    async optimizeArticle(content, settings) {
        try {
            // بناء البرومبت
            const prompt = this.buildOptimizationPrompt(content, settings);
            
            // تقسيم المحتوى إذا كان كبيراً
            const chunks = this.splitContentIfNeeded(content);
            
            if (chunks.length === 1) {
                // معالجة مقال واحد
                return await this.processWithRetry(prompt, {
                    temperature: 0.7,
                    maxTokens: 8192
                });
            } else {
                // معالجة متعددة الأجزاء
                return await this.processMultipleChunks(chunks, settings);
            }
            
        } catch (error) {
            console.error('خطأ في تحسين المقال:', error);
            throw error;
        }
    }

    // ===== بناء برومبت التحسين =====
    buildOptimizationPrompt(content, settings) {
        let prompt = PROMPT_TEMPLATES.seoOptimization;
        
        // استبدال المتغيرات
        prompt = prompt.replace('{originalContent}', content);
        prompt = prompt.replace('{keywordDensity}', settings.content.keywordDensity);
        prompt = prompt.replace('{minWordsPerParagraph}', settings.content.minWordsPerParagraph);
        prompt = prompt.replace('{writingStyle}', this.getWritingStyleDescription(settings.writingStyle));
        prompt = prompt.replace('{dialect}', this.getDialectDescription(settings.dialect));
        prompt = prompt.replace('{fontFamily}', settings.styling?.fontFamily || 'tajawal');
        prompt = prompt.replace('{headingStyle}', settings.styling?.headingStyle || 'gradient');
        
        // إضافة المتطلبات الإضافية
        const additionalRequirements = this.buildAdditionalRequirements(settings);
        prompt = prompt.replace('{additionalRequirements}', additionalRequirements);
        
        return prompt;
    }

    // ===== بناء المتطلبات الإضافية =====
    buildAdditionalRequirements(settings) {
        const requirements = [];
        
        if (settings.optimization.addBulletPoints) {
            requirements.push('- إضافة قوائم نقطية منظمة');
        }
        
        if (settings.optimization.addTables) {
            requirements.push('- إضافة جداول HTML منسقة للمعلومات المهمة');
        }
        
        if (settings.optimization.simplifyTerms) {
            requirements.push('- تبسيط المصطلحات المعقدة');
        }
        
        if (settings.optimization.enrichContent) {
            requirements.push('- إثراء المحتوى بمعلومات إضافية مفيدة');
        }
        
        if (settings.optimization.addExternalSources) {
            requirements.push('- إضافة مراجع ومصادر خارجية موثوقة');
        }
        
        if (settings.icons.enabled && settings.icons.density !== 'none') {
            const densityText = {
                'low': 'قليلة ومختارة بعناية',
                'medium': 'متوسطة ومناسبة',
                'high': 'كثيفة وتفاعلية'
            };
            requirements.push(`- إضافة رموز وأيقونات نصية ${densityText[settings.icons.density] || 'متوسطة'} (Unicode)`);
        }
        
        if (settings.diacritics) {
            requirements.push('- إضافة التشكيل للكلمات المهمة');
        }
        
        if (settings.optimization.embedCSS) {
            const cssRequirements = [
                `ألوان ${this.getColorSchemeDescription(settings.colorScheme)}`,
                `خط ${this.getFontDescription(settings.styling?.fontFamily)}`,
                `تنسيق عناوين ${this.getHeadingStyleDescription(settings.styling?.headingStyle)}`,
                `جداول ${this.getTableStyleDescription(settings.styling?.tableStyle)}`,
                'قوائم وأقتباسات محسنة'
            ];
            requirements.push(`- تضمين CSS مدمج متقدم: ${cssRequirements.join(', ')}`);
        }
        
        if (settings.additionalInstructions) {
            requirements.push(`- تعليمات إضافية: ${settings.additionalInstructions}`);
        }
        
        return requirements.join('\n');
    }

    // ===== وصف أنواع الصياغة =====
    getWritingStyleDescription(style) {
        const styles = {
            professional: 'احترافي ورسمي',
            friendly: 'ودود ومألوف',
            scary: 'تحذيري ومخيف',
            promotional: 'دعائي وتسويقي',
            academic: 'أكاديمي وعلمي',
            news: 'إخباري ومحايد',
            legal: 'قانوني ودقيق',
            medical: 'طبي ومتخصص'
        };
        return styles[style] || 'احترافي';
    }

    // ===== وصف اللهجات =====
    getDialectDescription(dialect) {
        const dialects = {
            formal: 'العربية الفصحى',
            gulf: 'اللهجة الخليجية',
            egyptian: 'اللهجة المصرية',
            libyan: 'اللهجة الليبية',
            sudanese: 'اللهجة السودانية'
        };
        return dialects[dialect] || 'العربية الفصحى';
    }

    // ===== وصف أنظمة الألوان =====
    getColorSchemeDescription(scheme) {
        const schemes = {
            'blue-orange-red': 'الأزرق والبرتقالي والأحمر',
            'blue': 'الأزرق بدرجاته',
            'black': 'الأسود والرمادي'
        };
        return schemes[scheme] || 'الأزرق والبرتقالي والأحمر';
    }

    // ===== وصف الخطوط =====
    getFontDescription(font) {
        const fonts = {
            'tajawal': 'Tajawal الأنيق',
            'cairo': 'Cairo الحديث',
            'amiri': 'Amiri التقليدي',
            'noto': 'Noto Sans Arabic العالمي'
        };
        return fonts[font] || 'Tajawal';
    }

    // ===== وصف أنماط العناوين =====
    getHeadingStyleDescription(style) {
        const styles = {
            'gradient': 'تدرج لوني جذاب',
            'underline': 'خط سفلي أنيق',
            'border-left': 'خط جانبي ملون',
            'shadow': 'ظل ناعم',
            'highlight': 'تظليل مميز'
        };
        return styles[style] || 'تدرج لوني';
    }

    // ===== وصف أنماط الجداول =====
    getTableStyleDescription(style) {
        const styles = {
            'modern': 'حديثة وأنيقة',
            'classic': 'كلاسيكية مهنية',
            'minimal': 'بسيطة ونظيفة',
            'colorful': 'ملونة وجذابة'
        };
        return styles[style] || 'حديثة';
    }

    // ===== تقسيم المحتوى الكبير =====
    splitContentIfNeeded(content, maxTokens = 25000) {
        // تقدير تقريبي: 4 أحرف = 1 توكن
        const estimatedTokens = content.length / 4;
        
        if (estimatedTokens <= maxTokens) {
            return [content];
        }
        
        return window.FileManager.splitLargeContent(content, maxTokens * 4);
    }

    // ===== معالجة أجزاء متعددة =====
    async processMultipleChunks(chunks, settings) {
        const results = [];
        
        for (let i = 0; i < chunks.length; i++) {
            const prompt = this.buildOptimizationPrompt(chunks[i], settings);
            
            // إضافة سياق للأجزاء المتعددة
            const contextualPrompt = `
هذا الجزء ${i + 1} من ${chunks.length} من مقال كبير.
يرجى تحسين هذا الجزء مع مراعاة أنه جزء من مقال أكبر.

${prompt}
`;
            
            const result = await this.processWithRetry(contextualPrompt, {
                temperature: 0.7,
                maxTokens: 8192
            });
            
            results.push(result);
            
            // انتظار بين الطلبات
            if (i < chunks.length - 1) {
                await this.delay(this.config.REQUEST_DELAY);
            }
        }
        
        // دمج النتائج
        return this.mergeChunkResults(results);
    }

    // ===== دمج نتائج الأجزاء =====
    mergeChunkResults(results) {
        const successfulResults = results.filter(r => r.success);
        
        if (successfulResults.length === 0) {
            return {
                success: false,
                error: 'فشل في معالجة جميع الأجزاء'
            };
        }
        
        const mergedContent = successfulResults.map(r => r.content).join('\n\n');
        const totalUsage = successfulResults.reduce((acc, r) => ({
            promptTokens: acc.promptTokens + (r.usage?.promptTokens || 0),
            completionTokens: acc.completionTokens + (r.usage?.completionTokens || 0),
            totalTokens: acc.totalTokens + (r.usage?.totalTokens || 0)
        }), { promptTokens: 0, completionTokens: 0, totalTokens: 0 });
        
        return {
            success: true,
            content: mergedContent,
            usage: totalUsage,
            chunksProcessed: successfulResults.length,
            totalChunks: results.length
        };
    }

    // ===== استخراج الكلمات المفتاحية =====
    async extractKeywords(content) {
        const prompt = PROMPT_TEMPLATES.keywordExtraction.replace('{content}', content);
        
        try {
            const result = await this.processWithRetry(prompt, {
                temperature: 0.3,
                maxTokens: 2048
            });
            
            if (result.success) {
                // محاولة تحليل JSON
                try {
                    const keywords = JSON.parse(result.content);
                    return {
                        success: true,
                        keywords: keywords
                    };
                } catch {
                    // إذا فشل تحليل JSON، إرجاع النص كما هو
                    return {
                        success: true,
                        keywords: { raw: result.content }
                    };
                }
            }
            
            return result;
            
        } catch (error) {
            console.error('خطأ في استخراج الكلمات المفتاحية:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // ===== دالة التأخير =====
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ===== الحصول على إحصائيات الاستخدام =====
    getUsageStats() {
        const stats = {
            totalKeys: this.config.API_KEYS.length,
            activeKeys: 0,
            blockedKeys: 0,
            totalRequestsToday: 0,
            currentKey: this.currentKeyIndex
        };
        
        this.keyUsage.forEach((usage, index) => {
            if (usage.isBlocked) {
                stats.blockedKeys++;
            } else {
                stats.activeKeys++;
            }
            stats.totalRequestsToday += usage.requestsToday;
        });
        
        return stats;
    }

    // ===== إعادة تعيين إحصائيات المفتاح =====
    resetKeyStats(keyIndex) {
        if (this.keyUsage.has(keyIndex)) {
            const usage = this.keyUsage.get(keyIndex);
            usage.errors = 0;
            usage.isBlocked = false;
            usage.requestsThisMinute = 0;
            usage.lastMinuteReset = Date.now();
        }
    }
}

// ===== إنشاء مثيل عام =====
window.AIEngine = new AIEngine();

console.log('تم تحميل محرك الذكاء الصناعي بنجاح');
