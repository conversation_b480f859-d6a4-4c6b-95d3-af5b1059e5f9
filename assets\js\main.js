/**
 * الملف الرئيسي لأداة تحسين المقالات للسيو
 * يحتوي على منطق التطبيق الأساسي وإدارة الواجهة
 */

// ===== متغيرات عامة =====
let currentSettings = DEFAULT_SETTINGS;
let uploadedFiles = [];
let processedResults = [];

// ===== تهيئة التطبيق =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// ===== دالة تهيئة التطبيق =====
function initializeApp() {
    // تشغيل أنيميشن التحميل المتقدم
    animateLoadingScreen();

    // إخفاء شاشة التحميل
    setTimeout(() => {
        document.getElementById('loadingScreen').style.opacity = '0';

        setTimeout(() => {
            document.getElementById('loadingScreen').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';
            document.getElementById('mainApp').classList.add('fade-in');

            // تحميل الإعدادات المحفوظة
            currentSettings = ConfigHelper.loadSettings();

            // تهيئة الواجهات
            initializeUploadSection();
            initializeOptionsSection();
            initializeResultsSection();

            // ربط الأحداث
            bindEvents();

            // إنشاء حاوي التنبيهات
            UIHelper.createAlertContainer();

            // إعداد الخيارات المشروطة
            setupConditionalOptions();

            // إعداد التبويبات والتفاعل
            setupUIInteractions();

        }, 500);
    }, 3500);

    console.log('تم تهيئة التطبيق بنجاح');
}

// تشغيل أنيميشن شاشة التحميل
function animateLoadingScreen() {
    const features = document.querySelectorAll('.feature-item');

    // تفعيل العناصر تدريجياً
    features.forEach((feature, index) => {
        setTimeout(() => {
            feature.style.opacity = '1';
            feature.style.transform = 'translateX(0)';
        }, 1000 + (index * 500));
    });

    // تحديث شريط التقدم
    const loadingBar = document.querySelector('.loading-bar');
    if (loadingBar) {
        setTimeout(() => {
            loadingBar.style.width = '30%';
        }, 1000);

        setTimeout(() => {
            loadingBar.style.width = '60%';
        }, 2000);

        setTimeout(() => {
            loadingBar.style.width = '100%';
        }, 3000);
    }
}

// إعداد التفاعل مع الواجهة
function setupUIInteractions() {
    // تبويبات طرق الرفع
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('method-tab')) {
            const method = e.target.dataset.method;
            switchUploadMethod(method);
        }
    });

    // منطقة السحب والإفلات
    const uploadZone = document.getElementById('uploadZone');
    if (uploadZone) {
        uploadZone.addEventListener('dragover', handleDragOver);
        uploadZone.addEventListener('dragleave', handleDragLeave);
        uploadZone.addEventListener('drop', handleDrop);
        uploadZone.addEventListener('click', () => document.getElementById('articleFiles').click());
    }

    // مراقبة تغيير النص المُلصق
    const pasteContent = document.getElementById('pasteContent');
    if (pasteContent) {
        pasteContent.addEventListener('input', updatePasteStats);
    }

    // تحديث إحصائيات الهيدر
    updateHeaderStats();
}

// تبديل طريقة الرفع
function switchUploadMethod(method) {
    // إزالة الفئة النشطة من جميع التبويبات
    document.querySelectorAll('.method-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // إخفاء جميع اللوحات
    document.querySelectorAll('.method-panel').forEach(panel => {
        panel.classList.remove('active');
    });

    // تفعيل التبويب واللوحة المحددة
    document.querySelector(`[data-method="${method}"]`).classList.add('active');
    document.getElementById(`${method}-panel`).classList.add('active');
}

// معالجة السحب فوق المنطقة
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

// معالجة مغادرة منطقة السحب
function handleDragLeave(e) {
    e.currentTarget.classList.remove('dragover');
}

// معالجة إسقاط الملفات
function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');

    const files = e.dataTransfer.files;
    handleFileSelection(files);
}

// معالجة اختيار الملفات
function handleFileSelection(files) {
    const filesList = document.getElementById('filesList');
    const filesContainer = document.getElementById('filesContainer');

    if (files.length > 0) {
        filesList.style.display = 'block';
        filesContainer.innerHTML = '';

        Array.from(files).forEach((file, index) => {
            const fileItem = createFileItem(file, index);
            filesContainer.appendChild(fileItem);
        });

        updateFileStats(files);
    }
}

// إنشاء عنصر ملف
function createFileItem(file, index) {
    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.innerHTML = `
        <div class="file-info">
            <div class="file-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="file-details">
                <h6>${file.name}</h6>
                <small>${formatFileSize(file.size)} • ${file.type || 'نص'}</small>
            </div>
        </div>
        <div class="file-actions">
            <button class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    return fileItem;
}

// تحديث إحصائيات الملفات
function updateFileStats(files) {
    const fileCount = document.getElementById('fileCount');
    const totalSize = document.getElementById('totalSize');

    if (fileCount) fileCount.textContent = files.length;

    if (totalSize) {
        const total = Array.from(files).reduce((sum, file) => sum + file.size, 0);
        totalSize.textContent = Math.round(total / 1024);
    }
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// تحديث إحصائيات النص المُلصق
function updatePasteStats() {
    const content = document.getElementById('pasteContent').value;
    const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
    const charCount = content.length;

    document.getElementById('pasteWordCount').textContent = wordCount;
    document.getElementById('pasteCharCount').textContent = charCount;
}

// تحديث إحصائيات الهيدر
function updateHeaderStats() {
    const totalArticles = document.getElementById('totalArticles');
    const avgScore = document.getElementById('avgScore');
    const totalTime = document.getElementById('totalTime');

    if (totalArticles) totalArticles.textContent = processedResults.length;

    if (avgScore && processedResults.length > 0) {
        const avg = processedResults.reduce((sum, result) =>
            sum + (result.improvementReport?.score || 0), 0) / processedResults.length;
        avgScore.textContent = Math.round(avg) + '%';
    }

    if (totalTime && processedResults.length > 0) {
        const total = processedResults.reduce((sum, result) =>
            sum + (result.processingTime || 0), 0);
        totalTime.textContent = Math.round(total / 1000);
    }
}

// إعداد الخيارات المشروطة
function setupConditionalOptions() {
    // إظهار/إخفاء نص حقوق النشر
    const addCopyrightCheckbox = document.getElementById('addCopyright');
    const copyrightTextDiv = document.getElementById('copyrightTextDiv');

    if (addCopyrightCheckbox && copyrightTextDiv) {
        addCopyrightCheckbox.addEventListener('change', function() {
            copyrightTextDiv.style.display = this.checked ? 'block' : 'none';
        });
    }

    // إظهار/إخفاء اللغة الثانوية
    const createMultiLanguageCheckbox = document.getElementById('createMultiLanguage');
    const secondaryLanguageDiv = document.getElementById('secondaryLanguageDiv');

    if (createMultiLanguageCheckbox && secondaryLanguageDiv) {
        createMultiLanguageCheckbox.addEventListener('change', function() {
            secondaryLanguageDiv.style.display = this.checked ? 'block' : 'none';
        });
    }
}

// ===== تهيئة قسم رفع الملفات =====
function initializeUploadSection() {
    const uploadSection = document.getElementById('uploadSection');
    uploadSection.innerHTML = `
        <div class="upload-container-premium">
            <div class="upload-header">
                <div class="upload-title">
                    <div class="title-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="title-content">
                        <h3>رفع المقالات للتحسين</h3>
                        <p>ارفع ملفاتك النصية أو الصق المحتوى مباشرة</p>
                    </div>
                </div>

                <div class="upload-stats">
                    <div class="stat-badge">
                        <i class="fas fa-file-alt"></i>
                        <span id="fileCount">0</span>
                        <small>ملف</small>
                    </div>
                    <div class="stat-badge">
                        <i class="fas fa-weight"></i>
                        <span id="totalSize">0</span>
                        <small>KB</small>
                    </div>
                </div>
            </div>

            <div class="upload-methods">
                <div class="method-tabs">
                    <button class="method-tab active" data-method="files">
                        <i class="fas fa-file-upload"></i>
                        <span>رفع ملفات</span>
                    </button>
                    <button class="method-tab" data-method="paste">
                        <i class="fas fa-paste"></i>
                        <span>لصق النص</span>
                    </button>
                    <button class="method-tab" data-method="url">
                        <i class="fas fa-link"></i>
                        <span>من رابط</span>
                    </button>
                </div>

                <div class="method-content">
                    <div class="method-panel active" id="files-panel">
                        ${generateFileUploadHTML()}
                    </div>
                    <div class="method-panel" id="paste-panel">
                        ${generatePasteHTML()}
                    </div>
                    <div class="method-panel" id="url-panel">
                        ${generateURLHTML()}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- رفع الملفات -->
            <div class="col-md-6">
                <div class="card h-100 hover-effect">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-upload"></i>
                            رفع الملفات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>اسحب الملفات هنا أو انقر للاختيار</h5>
                                <p class="text-muted">
                                    الملفات المدعومة: TXT, HTML, XLSX, CSV<br>
                                    الحد الأقصى: 50 ملف، 10 ميجابايت لكل ملف
                                </p>
                                <input type="file" id="fileInput" multiple 
                                       accept=".txt,.html,.htm,.xlsx,.xls,.csv" 
                                       style="display: none;">
                                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                    <i class="fas fa-folder-open"></i>
                                    اختيار الملفات
                                </button>
                            </div>
                        </div>
                        
                        <!-- قائمة الملفات المرفوعة -->
                        <div id="uploadedFilesList" class="mt-3" style="display: none;">
                            <h6>الملفات المرفوعة:</h6>
                            <div id="filesList"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إضافة نص مباشر -->
            <div class="col-md-6">
                <div class="card h-100 hover-effect">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-edit"></i>
                            إضافة نص مباشر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="articleTitle" class="form-label">عنوان المقال</label>
                            <input type="text" class="form-control" id="articleTitle" 
                                   placeholder="أدخل عنوان المقال...">
                        </div>
                        <div class="mb-3">
                            <label for="articleContent" class="form-label">محتوى المقال</label>
                            <textarea class="form-control" id="articleContent" rows="10" 
                                      placeholder="الصق محتوى المقال هنا..."></textarea>
                        </div>
                        <button class="btn btn-secondary" id="addDirectTextBtn">
                            <i class="fas fa-plus"></i>
                            إضافة المقال
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- تعليمات إضافية -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-instructions"></i>
                            تعليمات إضافية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="additionalInstructions" class="form-label">
                                تعليمات خاصة للتحسين (اختياري)
                            </label>
                            <textarea class="form-control" id="additionalInstructions" rows="3" 
                                      placeholder="أضف أي تعليمات خاصة تريد مراعاتها أثناء التحسين..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- زر البدء -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <button class="btn btn-success btn-lg" id="startProcessingBtn" disabled>
                    <i class="fas fa-play"></i>
                    بدء عملية التحسين
                </button>
            </div>
        </div>
    `;
    
    // تطبيق الأنماط على منطقة الرفع
    const uploadArea = document.getElementById('uploadArea');
    uploadArea.style.cssText = `
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 40px 20px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        background: #f8f9fa;
    `;
}

// ===== إنشاء HTML لرفع الملفات =====
function generateFileUploadHTML() {
    return `
        <div class="upload-zone" id="uploadZone">
            <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <h4>اسحب الملفات هنا أو انقر للاختيار</h4>
            <p>يدعم ملفات TXT, DOC, DOCX حتى 10MB لكل ملف</p>
            <input type="file" id="articleFiles" multiple accept=".txt,.doc,.docx" style="display: none;">
            <button class="btn btn-premium" onclick="document.getElementById('articleFiles').click()">
                <i class="fas fa-folder-open"></i>
                اختيار الملفات
            </button>
        </div>

        <div class="files-list" id="filesList" style="display: none;">
            <h5><i class="fas fa-list"></i> الملفات المحددة</h5>
            <div class="files-container" id="filesContainer"></div>
        </div>
    `;
}

// ===== إنشاء HTML للصق النص =====
function generatePasteHTML() {
    return `
        <div class="paste-zone">
            <div class="paste-header">
                <h4><i class="fas fa-paste"></i> الصق المحتوى مباشرة</h4>
                <p>انسخ والصق النص الذي تريد تحسينه</p>
            </div>

            <div class="paste-content">
                <textarea class="form-control form-control-premium" id="pasteContent"
                          rows="12" placeholder="الصق محتوى المقال هنا..."></textarea>

                <div class="paste-actions">
                    <button class="btn btn-premium" onclick="addPastedContent()">
                        <i class="fas fa-plus"></i>
                        إضافة المحتوى
                    </button>
                    <button class="btn btn-premium-outline" onclick="clearPasteContent()">
                        <i class="fas fa-trash"></i>
                        مسح
                    </button>
                </div>

                <div class="paste-stats">
                    <span class="stat-item">
                        <i class="fas fa-font"></i>
                        <span id="pasteWordCount">0</span> كلمة
                    </span>
                    <span class="stat-item">
                        <i class="fas fa-align-left"></i>
                        <span id="pasteCharCount">0</span> حرف
                    </span>
                </div>
            </div>
        </div>
    `;
}

// ===== إنشاء HTML للرفع من رابط =====
function generateURLHTML() {
    return `
        <div class="url-zone">
            <div class="url-header">
                <h4><i class="fas fa-link"></i> استيراد من رابط</h4>
                <p>أدخل رابط المقال أو الصفحة لاستخراج المحتوى</p>
            </div>

            <div class="url-content">
                <div class="input-group input-group-premium">
                    <input type="url" class="form-control form-control-premium" id="articleURL"
                           placeholder="https://example.com/article">
                    <button class="btn btn-premium" onclick="fetchFromURL()">
                        <i class="fas fa-download"></i>
                        استخراج
                    </button>
                </div>

                <div class="url-options">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="extractImages">
                        <label class="form-check-label" for="extractImages">
                            استخراج الصور أيضاً
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="cleanHTML" checked>
                        <label class="form-check-label" for="cleanHTML">
                            تنظيف HTML تلقائياً
                        </label>
                    </div>
                </div>

                <div class="url-preview" id="urlPreview" style="display: none;">
                    <h6>معاينة المحتوى المستخرج:</h6>
                    <div class="preview-content" id="previewContent"></div>
                    <button class="btn btn-premium" onclick="addURLContent()">
                        <i class="fas fa-check"></i>
                        إضافة هذا المحتوى
                    </button>
                </div>
            </div>
        </div>
    `;
}

// ===== تهيئة قسم الخيارات المحسن =====
function initializeOptionsSection() {
    const optionsSection = document.getElementById('optionsSection');
    optionsSection.innerHTML = `
        <div class="options-container-premium">
            <div class="options-header">
                <div class="options-title">
                    <div class="title-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="title-content">
                        <h3>إعدادات التحسين المتقدمة</h3>
                        <p>خصص إعدادات التحسين للحصول على أفضل النتائج</p>
                    </div>
                </div>

                <div class="options-actions">
                    <button class="btn btn-premium-sm" onclick="resetToDefaults()">
                        <i class="fas fa-undo"></i>
                        <span>الإعدادات الافتراضية</span>
                    </button>
                    <button class="btn btn-premium-outline-sm" onclick="savePreset()">
                        <i class="fas fa-save"></i>
                        <span>حفظ كقالب</span>
                    </button>
                </div>
            </div>

            <div class="options-tabs">
                <nav class="nav nav-pills nav-premium" role="tablist">
                    <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#basic-settings" type="button">
                        <i class="fas fa-sliders-h"></i>
                        <span>الإعدادات الأساسية</span>
                    </button>
                    <button class="nav-link" data-bs-toggle="pill" data-bs-target="#styling-settings" type="button">
                        <i class="fas fa-palette"></i>
                        <span>التنسيق والألوان</span>
                    </button>
                    <button class="nav-link" data-bs-toggle="pill" data-bs-target="#content-settings" type="button">
                        <i class="fas fa-file-alt"></i>
                        <span>المحتوى واللغة</span>
                    </button>
                    <button class="nav-link" data-bs-toggle="pill" data-bs-target="#advanced-settings" type="button">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات المتقدمة</span>
                    </button>
                </nav>
            </div>

            <div class="tab-content options-content">
                <div class="tab-pane fade show active" id="basic-settings">
                    ${generateBasicSettingsHTML()}
                </div>
                <div class="tab-pane fade" id="styling-settings">
                    ${generateStylingSettingsHTML()}
                </div>
                <div class="tab-pane fade" id="content-settings">
                    ${generateContentSettingsHTML()}
                </div>
                <div class="tab-pane fade" id="advanced-settings">
                    ${generateAdvancedSettingsHTML()}
                </div>
            </div>
        </div>
        
        <!-- خيارات التحسين الأساسية -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">خيارات التحسين الأساسية</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="languageImprovement" checked>
                            <label class="form-check-label" for="languageImprovement">
                                التحسين اللغوي للمقال
                            </label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="simplifyTerms">
                            <label class="form-check-label" for="simplifyTerms">
                                تبسيط المصطلحات
                            </label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="addBulletPoints" checked>
                            <label class="form-check-label" for="addBulletPoints">
                                إضافة قوائم نقطية
                            </label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="addTables" checked>
                            <label class="form-check-label" for="addTables">
                                إضافة جداول للمحتوى
                            </label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="createNewTitle" checked>
                            <label class="form-check-label" for="createNewTitle">
                                صياغة عنوان جديد احترافي
                            </label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="embedCSS" checked>
                            <label class="form-check-label" for="embedCSS">
                                تضمين تحسينات CSS
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">خيارات التحسين المتقدمة</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enrichContent">
                            <label class="form-check-label" for="enrichContent">
                                إثراء المحتوى بمعلومات إضافية
                            </label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="addExternalSources">
                            <label class="form-check-label" for="addExternalSources">
                                تضمين مصادر خارجية
                            </label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="addIcons" checked>
                            <label class="form-check-label" for="addIcons">
                                إضافة رموز وأيقونات معبرة
                            </label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="addDiacritics">
                            <label class="form-check-label" for="addDiacritics">
                                إضافة التشكيل للنصوص
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إعدادات الصياغة -->
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">نوع الصياغة</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="writingStyle">
                            <option value="professional">احترافي</option>
                            <option value="friendly">ودي</option>
                            <option value="scary">تخويف</option>
                            <option value="promotional">دعائي</option>
                            <option value="academic">أكاديمي</option>
                            <option value="news">إخباري</option>
                            <option value="legal">قانوني</option>
                            <option value="medical">طبي</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">لهجة الصياغة</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="dialect">
                            <option value="formal">فصحى</option>
                            <option value="gulf">خليجي</option>
                            <option value="egyptian">مصري</option>
                            <option value="libyan">ليبي</option>
                            <option value="sudanese">سوداني</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">نظام الألوان</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="colorScheme">
                            <option value="blue-orange-red">أزرق وبرتقالي وأحمر</option>
                            <option value="blue">أزرق بدرجاته</option>
                            <option value="black">أسود</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات التنسيق المتقدمة -->
        <div class="row">
            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">نوع الخط</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="fontFamily">
                            <option value="tajawal">Tajawal</option>
                            <option value="cairo">Cairo</option>
                            <option value="amiri">Amiri</option>
                            <option value="noto">Noto Sans Arabic</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">تنسيق العناوين</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="headingStyle">
                            <option value="gradient">تدرج لوني</option>
                            <option value="underline">خط سفلي كامل</option>
                            <option value="underline-first-word">خط سفلي للكلمة الأولى</option>
                            <option value="border-left" selected>خط جانبي</option>
                            <option value="shadow">ظل</option>
                            <option value="highlight">تظليل</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">كثافة الأيقونات</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="iconDensity">
                            <option value="none">بدون أيقونات</option>
                            <option value="low">قليلة</option>
                            <option value="medium">متوسطة</option>
                            <option value="high">كثيفة</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">تنسيق الجداول</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="tableStyle">
                            <option value="modern">حديث</option>
                            <option value="classic">كلاسيكي</option>
                            <option value="minimal">بسيط</option>
                            <option value="colorful">ملون</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات الألوان والتمييز -->
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">لون العناوين الرئيسية</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="headingColor">
                            <option value="#1e40af">أزرق داكن (افتراضي)</option>
                            <option value="#2563eb">أزرق متوسط</option>
                            <option value="#3b82f6">أزرق فاتح</option>
                            <option value="#1f2937">رمادي داكن</option>
                            <option value="#dc2626">أحمر</option>
                            <option value="#059669">أخضر</option>
                            <option value="#7c3aed">بنفسجي</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">لون العناوين الفرعية</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="subheadingColor">
                            <option value="#3b82f6">أزرق فاتح (افتراضي)</option>
                            <option value="#1e40af">أزرق داكن</option>
                            <option value="#2563eb">أزرق متوسط</option>
                            <option value="#6b7280">رمادي</option>
                            <option value="#ef4444">أحمر فاتح</option>
                            <option value="#10b981">أخضر فاتح</option>
                            <option value="#8b5cf6">بنفسجي فاتح</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">تمييز الكلمات المفتاحية</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="keywordHighlight">
                            <option value="bold">خط سميك (افتراضي)</option>
                            <option value="color">لون مميز</option>
                            <option value="background">خلفية ملونة</option>
                            <option value="underline">خط سفلي</option>
                            <option value="none">بدون تمييز</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات التنسيق المتقدم -->
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">أنماط القوائم</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="listStyle">
                            <option value="enhanced">محسنة (افتراضي)</option>
                            <option value="simple">بسيطة</option>
                            <option value="boxed">في صناديق</option>
                            <option value="highlighted">مظللة</option>
                            <option value="quote-style">نمط الاقتباس</option>
                            <option value="numbered-boxes">صناديق مرقمة</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">محاذاة الجداول</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="tableAlignment">
                            <option value="center">توسيط (افتراضي)</option>
                            <option value="right">يمين</option>
                            <option value="left">يسار</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">اتجاه النص</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="textDirection">
                            <option value="rtl-justify">يمين لليسار مع ضبط (افتراضي)</option>
                            <option value="rtl">يمين لليسار</option>
                            <option value="ltr">يسار لليمين</option>
                            <option value="center-justify">توسيط مع ضبط</option>
                            <option value="center">توسيط</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات المحتوى المتقدم -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">إعدادات إضافية</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="addConclusion" checked>
                            <label class="form-check-label" for="addConclusion">
                                إضافة خاتمة مميزة للمقال
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="addDiacritics">
                            <label class="form-check-label" for="addDiacritics">
                                إضافة التشكيل للنصوص
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="addCopyright">
                            <label class="form-check-label" for="addCopyright">
                                إضافة حقوق النشر والطبع
                            </label>
                        </div>

                        <div class="mb-3" id="copyrightTextDiv" style="display: none;">
                            <label for="copyrightText" class="form-label">نص حقوق النشر المخصص:</label>
                            <textarea class="form-control" id="copyrightText" rows="2" placeholder="© 2024 - جميع الحقوق محفوظة"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">إعدادات اللغة</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="language" class="form-label">لغة المقال:</label>
                            <select class="form-select" id="language">
                                <option value="arabic">العربية (افتراضي)</option>
                                <option value="english">الإنجليزية</option>
                                <option value="french">الفرنسية</option>
                                <option value="spanish">الإسبانية</option>
                                <option value="german">الألمانية</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="createMultiLanguage">
                            <label class="form-check-label" for="createMultiLanguage">
                                إنشاء نسخة بلغة أخرى
                            </label>
                        </div>

                        <div class="mb-3" id="secondaryLanguageDiv" style="display: none;">
                            <label for="secondaryLanguage" class="form-label">اللغة الثانوية:</label>
                            <select class="form-select" id="secondaryLanguage">
                                <option value="english">الإنجليزية</option>
                                <option value="arabic">العربية</option>
                                <option value="french">الفرنسية</option>
                                <option value="spanish">الإسبانية</option>
                                <option value="german">الألمانية</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات المصطلحات والمراجع -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">المصطلحات والاستبدال</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="replaceTerms" class="form-label">استبدال كلمات معينة (JSON):</label>
                            <textarea class="form-control" id="replaceTerms" rows="3" placeholder='{"كلمة قديمة": "كلمة جديدة", "مصطلح": "مصطلح محسن"}'></textarea>
                            <small class="form-text text-muted">مثال: {"AI": "الذكاء الصناعي", "SEO": "تحسين محركات البحث"}</small>
                        </div>

                        <div class="mb-3">
                            <label for="customTerms" class="form-label">مصطلحات مخصصة للإضافة:</label>
                            <textarea class="form-control" id="customTerms" rows="2" placeholder="مصطلح1, مصطلح2, مصطلح3"></textarea>
                            <small class="form-text text-muted">فصل بفاصلة</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">المراجع والإشارات</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="sourceReference" class="form-label">مرجع أو مصدر للإشارة إليه:</label>
                            <input type="text" class="form-control" id="sourceReference" placeholder="اسم الموقع أو المرجع">
                        </div>

                        <div class="mb-3">
                            <label for="contextualReference" class="form-label">إشارة سياقية لموقع أو محتوى:</label>
                            <input type="text" class="form-control" id="contextualReference" placeholder="رابط أو اسم الموقع">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات المحتوى -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">إعدادات المحتوى</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="minWordsPerParagraph" class="form-label">
                                الحد الأدنى لعدد الكلمات في كل فقرة
                            </label>
                            <input type="number" class="form-control" id="minWordsPerParagraph" 
                                   value="100" min="50" max="300">
                        </div>
                        <div class="mb-3">
                            <label for="keywordDensity" class="form-label">
                                كثافة الكلمات المفتاحية (%)
                            </label>
                            <input type="number" class="form-control" id="keywordDensity" 
                                   value="1" min="0.5" max="5" step="0.1">
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="headingStart" 
                                   id="startWithH2" value="h2" checked>
                            <label class="form-check-label" for="startWithH2">
                                البدء بـ H2
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="headingStart" 
                                   id="startWithH1" value="h1">
                            <label class="form-check-label" for="startWithH1">
                                البدء بـ H1
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">حفظ واستيراد الإعدادات</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" id="saveSettingsBtn">
                                <i class="fas fa-save"></i>
                                حفظ الإعدادات الحالية
                            </button>
                            <button class="btn btn-secondary" id="loadSettingsBtn">
                                <i class="fas fa-upload"></i>
                                استيراد إعدادات محفوظة
                            </button>
                            <button class="btn btn-outline-secondary" id="exportSettingsBtn">
                                <i class="fas fa-download"></i>
                                تصدير الإعدادات
                            </button>
                            <button class="btn btn-outline-danger" id="resetSettingsBtn">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين للافتراضي
                            </button>
                        </div>
                        <input type="file" id="settingsFileInput" accept=".json" style="display: none;">
                    </div>
                </div>
            </div>
        </div>
    `;
}

// ===== إنشاء HTML للإعدادات الأساسية =====
function generateBasicSettingsHTML() {
    return `
        <div class="settings-grid">
            <div class="setting-card">
                <div class="setting-header">
                    <div class="setting-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="setting-title">
                        <h5>نوع الصياغة</h5>
                        <p>اختر أسلوب الكتابة المناسب</p>
                    </div>
                </div>
                <div class="setting-content">
                    <select class="form-select form-select-premium" id="writingStyle">
                        <option value="professional">احترافي</option>
                        <option value="friendly">ودي</option>
                        <option value="persuasive">إقناعي</option>
                        <option value="academic">أكاديمي</option>
                        <option value="news">إخباري</option>
                        <option value="technical">تقني</option>
                    </select>
                </div>
            </div>

            <div class="setting-card">
                <div class="setting-header">
                    <div class="setting-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <div class="setting-title">
                        <h5>اللهجة</h5>
                        <p>اختر اللهجة المناسبة للجمهور</p>
                    </div>
                </div>
                <div class="setting-content">
                    <select class="form-select form-select-premium" id="dialect">
                        <option value="standard">فصحى</option>
                        <option value="gulf">خليجي</option>
                        <option value="egyptian">مصري</option>
                        <option value="levantine">شامي</option>
                        <option value="maghrebi">مغربي</option>
                    </select>
                </div>
            </div>

            <div class="setting-card">
                <div class="setting-header">
                    <div class="setting-icon">
                        <i class="fas fa-icons"></i>
                    </div>
                    <div class="setting-title">
                        <h5>كثافة الأيقونات</h5>
                        <p>مستوى استخدام الأيقونات</p>
                    </div>
                </div>
                <div class="setting-content">
                    <select class="form-select form-select-premium" id="iconDensity">
                        <option value="none">بدون أيقونات</option>
                        <option value="low">قليلة</option>
                        <option value="medium">متوسطة</option>
                        <option value="high">كثيفة</option>
                    </select>
                </div>
            </div>

            <div class="setting-card">
                <div class="setting-header">
                    <div class="setting-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="setting-title">
                        <h5>كثافة الكلمات المفتاحية</h5>
                        <p>النسبة المئوية المطلوبة</p>
                    </div>
                </div>
                <div class="setting-content">
                    <div class="input-group">
                        <input type="number" class="form-control form-control-premium" id="keywordDensity"
                               min="0.5" max="5" step="0.1" value="1">
                        <span class="input-group-text">%</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// ===== إنشاء HTML لإعدادات التنسيق =====
function generateStylingSettingsHTML() {
    return `<div class="settings-grid"><!-- محتوى التنسيق --></div>`;
}

// ===== إنشاء HTML لإعدادات المحتوى =====
function generateContentSettingsHTML() {
    return `<div class="settings-grid"><!-- محتوى اللغة --></div>`;
}

// ===== إنشاء HTML للإعدادات المتقدمة =====
function generateAdvancedSettingsHTML() {
    return `<div class="settings-grid"><!-- الإعدادات المتقدمة --></div>`;
}

// ===== تهيئة قسم النتائج =====
function initializeResultsSection() {
    const resultsSection = document.getElementById('resultsSection');
    resultsSection.innerHTML = `
        <div class="row">
            <div class="col-12">
                <h4 class="mb-4">
                    <i class="fas fa-chart-line text-primary"></i>
                    النتائج والتقارير
                </h4>
            </div>
        </div>

        <!-- Export Controls -->
        <div class="row mb-4" id="exportControls" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-download"></i>
                            تصدير النتائج
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-primary" onclick="exportResults('html')">
                                        <i class="fas fa-code"></i> HTML
                                    </button>
                                    <button class="btn btn-success" onclick="exportResults('excel')">
                                        <i class="fas fa-file-excel"></i> Excel
                                    </button>
                                    <button class="btn btn-secondary" onclick="exportResults('text')">
                                        <i class="fas fa-file-alt"></i> نص
                                    </button>
                                    <button class="btn btn-warning" onclick="exportResults('zip')">
                                        <i class="fas fa-file-archive"></i> ملف مضغوط شامل
                                    </button>
                                    <button class="btn btn-info" onclick="exportResults('all')">
                                        <i class="fas fa-download"></i> جميع الصيغ
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-outline-danger" onclick="clearResults()">
                                    <i class="fas fa-trash"></i> مسح النتائج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Summary -->
        <div class="row mb-4" id="resultsSummary" style="display: none;">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary" id="totalArticlesCount">0</h3>
                        <p class="mb-0">إجمالي المقالات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success" id="successfulCount">0</h3>
                        <p class="mb-0">تم تحسينها بنجاح</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning" id="averageScore">0%</h3>
                        <p class="mb-0">متوسط نقاط التحسين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info" id="totalWordsAdded">0</h3>
                        <p class="mb-0">إجمالي الكلمات المضافة</p>
                    </div>
                </div>
            </div>
        </div>

        <div id="resultsContent">
            <div class="text-center py-5">
                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد نتائج بعد</h5>
                <p class="text-muted">قم برفع المقالات وبدء عملية التحسين لعرض النتائج هنا</p>
            </div>
        </div>
    `;
}

// ===== ربط الأحداث =====
function bindEvents() {
    // أحداث رفع الملفات
    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.getElementById('uploadArea');
    
    fileInput.addEventListener('change', handleFileSelect);
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
    
    // أحداث الأزرار
    document.getElementById('addDirectTextBtn').addEventListener('click', handleAddDirectText);
    document.getElementById('startProcessingBtn').addEventListener('click', handleStartProcessing);
    document.getElementById('saveSettingsBtn').addEventListener('click', handleSaveSettings);
    document.getElementById('loadSettingsBtn').addEventListener('click', handleLoadSettings);
    document.getElementById('exportSettingsBtn').addEventListener('click', handleExportSettings);
    document.getElementById('resetSettingsBtn').addEventListener('click', handleResetSettings);
    
    // أحداث شريط التقدم
    document.getElementById('pauseBtn').addEventListener('click', handlePauseProcessing);
    document.getElementById('stopBtn').addEventListener('click', handleStopProcessing);
    
    // أحداث الإعدادات
    document.getElementById('settingsBtn').addEventListener('click', () => {
        document.getElementById('options-tab').click();
    });

    document.getElementById('helpBtn').addEventListener('click', showHelpModal);

    // أحداث استيراد الإعدادات
    document.getElementById('settingsFileInput').addEventListener('change', handleSettingsFileImport);
}

// ===== معالجة اختيار الملفات =====
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    processSelectedFiles(files);
}

// ===== معالجة سحب الملفات =====
function handleDragOver(event) {
    event.preventDefault();
    const uploadArea = event.currentTarget;
    uploadArea.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    const uploadArea = event.currentTarget;
    uploadArea.classList.remove('dragover');
}

function handleFileDrop(event) {
    event.preventDefault();
    const uploadArea = event.currentTarget;
    uploadArea.classList.remove('dragover');

    const files = Array.from(event.dataTransfer.files);
    processSelectedFiles(files);
}

// ===== معالجة الملفات المختارة =====
function processSelectedFiles(files) {
    const validFiles = [];
    const errors = [];
    
    files.forEach(file => {
        // التحقق من نوع الملف
        if (!Validator.isValidFileType(file.name, [...SUPPORTED_FILE_TYPES.text, ...SUPPORTED_FILE_TYPES.html, ...SUPPORTED_FILE_TYPES.excel])) {
            errors.push(`نوع الملف غير مدعوم: ${file.name}`);
            return;
        }
        
        // التحقق من حجم الملف
        if (!Validator.isValidFileSize(file.size, SUPPORTED_FILE_TYPES.maxFileSize)) {
            errors.push(`حجم الملف كبير جداً: ${file.name} (${Formatter.formatFileSize(file.size)})`);
            return;
        }
        
        validFiles.push(file);
    });
    
    // التحقق من العدد الإجمالي
    if (uploadedFiles.length + validFiles.length > SUPPORTED_FILE_TYPES.maxFiles) {
        errors.push(`عدد الملفات كبير جداً. الحد الأقصى ${SUPPORTED_FILE_TYPES.maxFiles} ملف`);
        return;
    }
    
    // عرض الأخطاء
    errors.forEach(error => {
        UIHelper.showAlert(error, 'error');
    });
    
    // إضافة الملفات الصحيحة
    if (validFiles.length > 0) {
        uploadedFiles.push(...validFiles);
        updateFilesList();
        updateStartButton();
        UIHelper.showAlert(`تم رفع ${validFiles.length} ملف بنجاح`, 'success');
    }
}

// ===== تحديث قائمة الملفات =====
function updateFilesList() {
    const filesList = document.getElementById('filesList');
    const uploadedFilesList = document.getElementById('uploadedFilesList');
    
    if (uploadedFiles.length === 0) {
        uploadedFilesList.style.display = 'none';
        return;
    }
    
    uploadedFilesList.style.display = 'block';
    filesList.innerHTML = uploadedFiles.map((file, index) => `
        <div class="d-flex justify-content-between align-items-center border rounded p-2 mb-2">
            <div>
                <i class="fas fa-file-alt text-primary me-2"></i>
                <strong>${file.name}</strong>
                <small class="text-muted">(${Formatter.formatFileSize(file.size)})</small>
            </div>
            <button class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `).join('');
}

// ===== إزالة ملف =====
function removeFile(index) {
    uploadedFiles.splice(index, 1);
    updateFilesList();
    updateStartButton();
}

// ===== تحديث زر البدء =====
function updateStartButton() {
    const startBtn = document.getElementById('startProcessingBtn');
    const hasContent = uploadedFiles.length > 0 || document.getElementById('articleContent').value.trim();
    startBtn.disabled = !hasContent;
}

// ===== معالجة إضافة نص مباشر =====
function handleAddDirectText() {
    const title = document.getElementById('articleTitle').value.trim();
    const content = document.getElementById('articleContent').value.trim();
    
    if (!content) {
        UIHelper.showAlert('يرجى إدخال محتوى المقال', 'warning');
        return;
    }
    
    const articleFile = FileHelper.textToFile(
        `${title ? title + '\n\n' : ''}${content}`,
        `مقال_${Date.now()}.txt`,
        'text/plain'
    );
    
    uploadedFiles.push(articleFile);
    updateFilesList();
    updateStartButton();
    
    // مسح النموذج
    document.getElementById('articleTitle').value = '';
    document.getElementById('articleContent').value = '';
    
    UIHelper.showAlert('تم إضافة المقال بنجاح', 'success');
}

// ===== معالجة بدء المعالجة =====
function handleStartProcessing() {
    if (uploadedFiles.length === 0) {
        UIHelper.showAlert('يرجى رفع ملف واحد على الأقل', 'warning');
        return;
    }
    
    // جمع الإعدادات الحالية
    collectCurrentSettings();
    
    // بدء المعالجة
    startProcessing();
}

// ===== جمع الإعدادات الحالية =====
function collectCurrentSettings() {
    currentSettings = {
        optimization: {
            languageImprovement: document.getElementById('languageImprovement').checked,
            simplifyTerms: document.getElementById('simplifyTerms').checked,
            addBulletPoints: document.getElementById('addBulletPoints').checked,
            addTables: document.getElementById('addTables').checked,
            createNewTitle: document.getElementById('createNewTitle').checked,
            embedCSS: document.getElementById('embedCSS').checked,
            enrichContent: document.getElementById('enrichContent').checked,
            addExternalSources: document.getElementById('addExternalSources').checked
        },
        writingStyle: document.getElementById('writingStyle').value,
        dialect: document.getElementById('dialect').value,
        colorScheme: document.getElementById('colorScheme').value,
        icons: {
            enabled: document.getElementById('iconDensity').value !== 'none',
            density: document.getElementById('iconDensity').value,
            location: 'all'
        },

        styling: {
            fontFamily: document.getElementById('fontFamily').value,
            headingColor: document.getElementById('headingColor').value,
            subheadingColor: document.getElementById('subheadingColor').value,
            headingStyle: document.getElementById('headingStyle').value,
            tableStyle: document.getElementById('tableStyle').value,
            tableAlignment: document.getElementById('tableAlignment').value,
            listStyle: document.getElementById('listStyle').value,
            quoteStyle: 'modern',
            keywordHighlight: document.getElementById('keywordHighlight').value,
            textDirection: document.getElementById('textDirection').value,
            addConclusion: document.getElementById('addConclusion').checked,
            addCopyright: document.getElementById('addCopyright').checked,
            copyrightText: document.getElementById('copyrightText').value,
            addDiacritics: document.getElementById('addDiacritics').checked
        },
        diacritics: document.getElementById('addDiacritics').checked,
        content: {
            startWithH1: document.querySelector('input[name="headingStart"]:checked').value === 'h1',
            minWordsPerParagraph: parseInt(document.getElementById('minWordsPerParagraph').value),
            keywordDensity: parseFloat(document.getElementById('keywordDensity').value),
            minMainHeadings: 0,
            minSubHeadings: 0,
            language: document.getElementById('language').value,
            createMultiLanguage: document.getElementById('createMultiLanguage').checked,
            secondaryLanguage: document.getElementById('secondaryLanguage').value
        },
        additionalInstructions: document.getElementById('additionalInstructions').value.trim(),

        // المصطلحات والمراجع
        customTerms: {
            replaceTerms: document.getElementById('replaceTerms').value,
            customTerms: document.getElementById('customTerms').value,
            sourceReference: document.getElementById('sourceReference').value,
            contextualReference: document.getElementById('contextualReference').value
        }
    };
}

// ===== بدء المعالجة =====
function startProcessing() {
    // إظهار شريط التقدم
    document.getElementById('progressContainer').style.display = 'block';
    
    // تحديث حالة التطبيق
    APP_STATE.isProcessing = true;
    APP_STATE.isPaused = false;
    APP_STATE.currentProgress = 0;
    APP_STATE.totalArticles = uploadedFiles.length;
    APP_STATE.processedArticles = 0;
    APP_STATE.startTime = Date.now();
    
    // بدء المعالجة الفعلية
    if (window.ArticleProcessor) {
        window.ArticleProcessor.processArticles(uploadedFiles, currentSettings);
    } else {
        console.error('معالج المقالات غير متوفر');
        UIHelper.showAlert('خطأ في تحميل معالج المقالات', 'error');
    }
}

// ===== معالجة حفظ الإعدادات =====
function handleSaveSettings() {
    collectCurrentSettings();
    if (ConfigHelper.saveSettings(currentSettings)) {
        UIHelper.showAlert('تم حفظ الإعدادات بنجاح', 'success');
    } else {
        UIHelper.showAlert('خطأ في حفظ الإعدادات', 'error');
    }
}

// ===== معالجة تحميل الإعدادات =====
function handleLoadSettings() {
    document.getElementById('settingsFileInput').click();
}

// ===== معالجة استيراد ملف الإعدادات =====
function handleSettingsFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedSettings = JSON.parse(e.target.result);

            // التحقق من صحة الإعدادات
            if (ConfigHelper.validateSettings(importedSettings)) {
                currentSettings = ConfigHelper.mergeSettings(DEFAULT_SETTINGS, importedSettings);
                loadSettingsToUI();
                UIHelper.showAlert('تم استيراد الإعدادات بنجاح', 'success');
            } else {
                UIHelper.showAlert('ملف الإعدادات غير صحيح', 'error');
            }
        } catch (error) {
            UIHelper.showAlert('خطأ في قراءة ملف الإعدادات', 'error');
        }
    };
    reader.readAsText(file);

    // إعادة تعيين قيمة الإدخال
    event.target.value = '';
}

// ===== معالجة تصدير الإعدادات =====
function handleExportSettings() {
    collectCurrentSettings();
    const settingsJson = JSON.stringify(currentSettings, null, 2);
    const filename = `seo_tool_settings_${new Date().toISOString().split('T')[0]}.json`;
    FileHelper.downloadFile(settingsJson, filename, 'application/json');
    UIHelper.showAlert('تم تصدير الإعدادات بنجاح', 'success');
}

// ===== معالجة إعادة تعيين الإعدادات =====
function handleResetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
        currentSettings = Object.assign({}, DEFAULT_SETTINGS);
        loadSettingsToUI();
        UIHelper.showAlert('تم إعادة تعيين الإعدادات بنجاح', 'success');
    }
}

// ===== تحميل الإعدادات للواجهة =====
function loadSettingsToUI() {
    // تحميل خيارات التحسين
    document.getElementById('languageImprovement').checked = currentSettings.optimization.languageImprovement;
    document.getElementById('simplifyTerms').checked = currentSettings.optimization.simplifyTerms;
    document.getElementById('addBulletPoints').checked = currentSettings.optimization.addBulletPoints;
    document.getElementById('addTables').checked = currentSettings.optimization.addTables;
    document.getElementById('createNewTitle').checked = currentSettings.optimization.createNewTitle;
    document.getElementById('embedCSS').checked = currentSettings.optimization.embedCSS;
    document.getElementById('enrichContent').checked = currentSettings.optimization.enrichContent;
    document.getElementById('addExternalSources').checked = currentSettings.optimization.addExternalSources;
    
    // تحميل إعدادات الصياغة
    document.getElementById('writingStyle').value = currentSettings.writingStyle;
    document.getElementById('dialect').value = currentSettings.dialect;
    document.getElementById('colorScheme').value = currentSettings.colorScheme;

    // تحميل إعدادات التنسيق الجديدة
    if (currentSettings.styling) {
        document.getElementById('fontFamily').value = currentSettings.styling.fontFamily || 'tajawal';
        document.getElementById('headingStyle').value = currentSettings.styling.headingStyle || 'border-left';
        document.getElementById('tableStyle').value = currentSettings.styling.tableStyle || 'modern';
        document.getElementById('tableAlignment').value = currentSettings.styling.tableAlignment || 'center';
        document.getElementById('listStyle').value = currentSettings.styling.listStyle || 'enhanced';
        document.getElementById('headingColor').value = currentSettings.styling.headingColor || '#1e40af';
        document.getElementById('subheadingColor').value = currentSettings.styling.subheadingColor || '#3b82f6';
        document.getElementById('keywordHighlight').value = currentSettings.styling.keywordHighlight || 'bold';
        document.getElementById('textDirection').value = currentSettings.styling.textDirection || 'rtl-justify';
        document.getElementById('addConclusion').checked = currentSettings.styling.addConclusion !== false;
        document.getElementById('addCopyright').checked = currentSettings.styling.addCopyright || false;
        document.getElementById('copyrightText').value = currentSettings.styling.copyrightText || '';
        document.getElementById('addDiacritics').checked = currentSettings.styling.addDiacritics || false;

        // إظهار/إخفاء نص حقوق النشر
        document.getElementById('copyrightTextDiv').style.display =
            currentSettings.styling.addCopyright ? 'block' : 'none';
    }

    // تحميل إعدادات الأيقونات
    if (currentSettings.icons) {
        const iconDensity = currentSettings.icons.enabled ? currentSettings.icons.density : 'none';
        document.getElementById('iconDensity').value = iconDensity;
    }

    // تحميل إعدادات المحتوى
    document.getElementById('minWordsPerParagraph').value = currentSettings.content.minWordsPerParagraph;
    document.getElementById('keywordDensity').value = currentSettings.content.keywordDensity;

    const headingRadio = currentSettings.content.startWithH1 ? 'startWithH1' : 'startWithH2';
    document.getElementById(headingRadio).checked = true;

    // تحميل إعدادات اللغة
    if (currentSettings.content.language) {
        document.getElementById('language').value = currentSettings.content.language;
        document.getElementById('createMultiLanguage').checked = currentSettings.content.createMultiLanguage || false;
        document.getElementById('secondaryLanguage').value = currentSettings.content.secondaryLanguage || 'english';

        // إظهار/إخفاء اللغة الثانوية
        document.getElementById('secondaryLanguageDiv').style.display =
            currentSettings.content.createMultiLanguage ? 'block' : 'none';
    }

    // تحميل إعدادات المصطلحات
    if (currentSettings.customTerms) {
        document.getElementById('replaceTerms').value = currentSettings.customTerms.replaceTerms || '';
        document.getElementById('customTerms').value = currentSettings.customTerms.customTerms || '';
        document.getElementById('sourceReference').value = currentSettings.customTerms.sourceReference || '';
        document.getElementById('contextualReference').value = currentSettings.customTerms.contextualReference || '';
    }
}

// ===== معالجة إيقاف المعالجة مؤقتاً =====
function handlePauseProcessing() {
    APP_STATE.isPaused = !APP_STATE.isPaused;
    const pauseBtn = document.getElementById('pauseBtn');
    
    if (APP_STATE.isPaused) {
        pauseBtn.innerHTML = '<i class="fas fa-play"></i> استئناف';
        document.getElementById('currentStatus').textContent = 'متوقف مؤقتاً';
    } else {
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> إيقاف مؤقت';
        document.getElementById('currentStatus').textContent = 'جاري المعالجة';
    }
}

// ===== معالجة إيقاف المعالجة =====
function handleStopProcessing() {
    if (confirm('هل أنت متأكد من إيقاف عملية المعالجة؟')) {
        APP_STATE.isProcessing = false;
        APP_STATE.isPaused = false;
        document.getElementById('progressContainer').style.display = 'none';
        UIHelper.showAlert('تم إيقاف عملية المعالجة', 'info');
    }
}

// ===== إظهار نافذة المساعدة =====
function showHelpModal() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-question-circle text-primary"></i>
                        دليل الاستخدام
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="accordion" id="helpAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#help1">
                                    <i class="fas fa-upload me-2"></i>
                                    كيفية رفع المقالات
                                </button>
                            </h2>
                            <div id="help1" class="accordion-collapse collapse show" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <ul>
                                        <li><strong>رفع الملفات:</strong> اسحب الملفات أو انقر على "اختيار الملفات"</li>
                                        <li><strong>الملفات المدعومة:</strong> TXT, HTML, XLSX, CSV</li>
                                        <li><strong>الحد الأقصى:</strong> 50 ملف، 10 ميجابايت لكل ملف</li>
                                        <li><strong>إدخال مباشر:</strong> الصق المحتوى في النموذج المخصص</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help2">
                                    <i class="fas fa-sliders-h me-2"></i>
                                    خيارات التحسين
                                </button>
                            </h2>
                            <div id="help2" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <ul>
                                        <li><strong>التحسين اللغوي:</strong> تصحيح الأخطاء وتحسين التراكيب</li>
                                        <li><strong>القوائم والجداول:</strong> إضافة عناصر تنظيمية للمحتوى</li>
                                        <li><strong>نوع الصياغة:</strong> اختر الأسلوب المناسب لجمهورك</li>
                                        <li><strong>كثافة الكلمات المفتاحية:</strong> النسبة المئوية المثلى 1-2%</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help3">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير النتائج
                                </button>
                            </h2>
                            <div id="help3" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <ul>
                                        <li><strong>HTML:</strong> ملف منسق مع محرك بحث مدمج</li>
                                        <li><strong>Excel:</strong> جداول منظمة مع تحليلات مفصلة</li>
                                        <li><strong>نص:</strong> ملفات نصية بسيطة</li>
                                        <li><strong>جميع الصيغ:</strong> تصدير شامل لجميع الأنواع</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help4">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    استكشاف الأخطاء
                                </button>
                            </h2>
                            <div id="help4" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <ul>
                                        <li><strong>خطأ في رفع الملف:</strong> تحقق من نوع وحجم الملف</li>
                                        <li><strong>فشل في المعالجة:</strong> تحقق من الاتصال بالإنترنت</li>
                                        <li><strong>بطء في الاستجابة:</strong> قلل عدد المقالات أو انتظر قليلاً</li>
                                        <li><strong>مشاكل في التصدير:</strong> تأكد من دعم المتصفح للتحميل</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // إزالة النافذة عند الإغلاق
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// ===== إدارة النتائج =====

// تحديث عرض النتائج
function updateResultsDisplay(results) {
    if (!results || results.length === 0) return;

    // إظهار أقسام النتائج
    document.getElementById('exportControls').style.display = 'block';
    document.getElementById('resultsSummary').style.display = 'block';

    // تحديث الإحصائيات
    updateResultsStats(results);

    // عرض المقالات
    displayArticleResults(results);
}

// تحديث إحصائيات النتائج
function updateResultsStats(results) {
    const totalArticles = results.length;
    const successfulArticles = results.filter(r => r.optimizedContent).length;
    const averageScore = results.reduce((sum, r) => sum + (r.improvementReport?.score || 0), 0) / totalArticles;
    const totalWordsAdded = results.reduce((sum, r) => sum + (r.analysis?.improvements?.wordIncrease || 0), 0);

    document.getElementById('totalArticlesCount').textContent = totalArticles;
    document.getElementById('successfulCount').textContent = successfulArticles;
    document.getElementById('averageScore').textContent = Math.round(averageScore) + '%';
    document.getElementById('totalWordsAdded').textContent = totalWordsAdded;
}

// عرض نتائج المقالات
function displayArticleResults(results) {
    const resultsContent = document.getElementById('resultsContent');

    let html = '<div class="row">';

    results.forEach((article, index) => {
        const scoreClass = getScoreClass(article.improvementReport?.score || 0);

        html += `
            <div class="col-12 mb-4">
                <div class="card article-result-card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="mb-1">${article.optimizedTitle || article.originalTitle}</h5>
                                <small class="text-muted">
                                    <i class="fas fa-file"></i> ${article.filename} |
                                    <i class="fas fa-clock"></i> ${new Date(article.processedAt).toLocaleString('ar-SA')}
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="badge bg-${scoreClass} fs-6">
                                    ${article.improvementReport?.score || 0}% نقاط التحسين
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="optimized-preview">
                                    ${article.optimizedContent.substring(0, 500)}...
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-sm btn-primary" onclick="viewFullArticle(${index})">
                                        <i class="fas fa-eye"></i> عرض كامل
                                    </button>
                                    <button class="btn btn-sm btn-secondary" onclick="copyArticleContent(${index})">
                                        <i class="fas fa-copy"></i> نسخ
                                    </button>
                                    <button class="btn btn-sm btn-success" onclick="exportSingleArticle(${index})">
                                        <i class="fas fa-download"></i> تصدير
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6>إحصائيات التحسين:</h6>
                                <ul class="list-unstyled small">
                                    <li><strong>الكلمات:</strong> ${article.analysis?.originalStats?.wordCount || 0} → ${article.analysis?.optimizedStats?.wordCount || 0}</li>
                                    <li><strong>العناوين:</strong> ${article.analysis?.optimizedStats?.headings?.total || 0}</li>
                                    <li><strong>القوائم:</strong> ${article.analysis?.optimizedStats?.lists?.total || 0}</li>
                                    <li><strong>الجداول:</strong> ${article.analysis?.optimizedStats?.tables || 0}</li>
                                </ul>

                                ${article.improvementReport?.improvements ? `
                                <h6 class="mt-3">التحسينات المطبقة فعلياً:</h6>
                                <div class="improvements-list">
                                    ${article.improvementReport.improvements.slice(0, 5).map(imp => `
                                        <div class="improvement-item">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <span>${imp}</span>
                                        </div>
                                    `).join('')}
                                    ${article.improvementReport.improvements.length > 5 ? `
                                        <div class="improvement-item text-muted">
                                            <i class="fas fa-plus-circle me-2"></i>
                                            <span>و ${article.improvementReport.improvements.length - 5} تحسينات أخرى...</span>
                                        </div>
                                    ` : ''}
                                </div>

                                ${article.improvementReport?.keywordAnalysis ? `
                                <h6 class="mt-3">تحليل الكلمات المفتاحية:</h6>
                                <div class="keyword-analysis">
                                    ${article.improvementReport.keywordAnalysis.primary ? `
                                        <div class="primary-keyword mb-2">
                                            <span class="badge bg-primary me-2">الكلمة الرئيسية</span>
                                            <strong>"${article.improvementReport.keywordAnalysis.primary.keyword}"</strong>
                                            <small class="text-muted ms-2">
                                                (${article.improvementReport.keywordAnalysis.primary.occurrences} مرة،
                                                كثافة ${article.improvementReport.keywordAnalysis.primary.density.toFixed(1)}%)
                                            </small>
                                        </div>
                                        ${article.improvementReport.keywordAnalysis.locations.length > 0 ? `
                                            <div class="keyword-locations mb-2">
                                                <small class="text-info">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    موجودة في: ${article.improvementReport.keywordAnalysis.locations.join(', ')}
                                                </small>
                                            </div>
                                        ` : ''}
                                    ` : ''}

                                    ${article.improvementReport.keywordAnalysis.secondary.length > 0 ? `
                                        <div class="secondary-keywords">
                                            <span class="badge bg-secondary me-2">كلمات ثانوية</span>
                                            ${article.improvementReport.keywordAnalysis.secondary.slice(0, 3).map(kw =>
                                                `<span class="badge bg-outline-secondary me-1">${kw.keyword} (${kw.occurrences})</span>`
                                            ).join('')}
                                        </div>
                                    ` : ''}
                                </div>
                                ` : ''}
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    resultsContent.innerHTML = html;
}

// الحصول على فئة اللون حسب النقاط
function getScoreClass(score) {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    if (score >= 40) return 'info';
    return 'danger';
}

// عرض المقال كاملاً
function viewFullArticle(index) {
    const article = processedResults[index];
    if (!article) return;

    // إنشاء نافذة منبثقة
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${article.optimizedTitle || article.originalTitle}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- تبويبات المحتوى -->
                    <ul class="nav nav-tabs mb-3" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#content-${index}" type="button">
                                <i class="fas fa-file-alt"></i> المحتوى المحسن
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#analysis-${index}" type="button">
                                <i class="fas fa-chart-line"></i> التحليل المفصل
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#original-${index}" type="button">
                                <i class="fas fa-history"></i> المحتوى الأصلي
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content">
                        <!-- المحتوى المحسن -->
                        <div class="tab-pane fade show active" id="content-${index}">
                            <div class="article-full-content">
                                ${article.optimizedContent}
                            </div>
                        </div>

                        <!-- التحليل المفصل -->
                        <div class="tab-pane fade" id="analysis-${index}">
                            <div class="detailed-analysis">
                                ${this.generateDetailedAnalysisHTML(article)}
                            </div>
                        </div>

                        <!-- المحتوى الأصلي -->
                        <div class="tab-pane fade" id="original-${index}">
                            <div class="original-content-display">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    هذا هو المحتوى الأصلي قبل التحسين
                                </div>
                                <div class="original-text">
                                    ${this.formatOriginalContent(article.originalContent)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="copyArticleContent(${index})">نسخ المحتوى</button>
                    <button type="button" class="btn btn-success" onclick="exportSingleArticle(${index})">تصدير المقال</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // إزالة النافذة عند الإغلاق
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// نسخ محتوى المقال
function copyArticleContent(index) {
    const article = processedResults[index];
    if (!article) return;

    const textContent = TextHelper.extractTextFromHTML(article.optimizedContent);

    navigator.clipboard.writeText(textContent).then(() => {
        UIHelper.showAlert('تم نسخ المحتوى بنجاح', 'success');
    }).catch(() => {
        UIHelper.showAlert('فشل في نسخ المحتوى', 'error');
    });
}

// تصدير مقال واحد
function exportSingleArticle(index) {
    const article = processedResults[index];
    if (!article) {
        UIHelper.showAlert('المقال غير موجود', 'error');
        return;
    }

    // إنشاء قائمة خيارات التصدير
    const exportOptions = `
        <div class="export-options">
            <h5>اختر صيغة التصدير:</h5>
            <div class="btn-group-vertical w-100" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="exportSingleAs(${index}, 'html')">
                    <i class="fas fa-code"></i> HTML مع CSS
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportSingleAs(${index}, 'text')">
                    <i class="fas fa-file-text"></i> نص عادي
                </button>
                <button type="button" class="btn btn-outline-info" onclick="exportSingleAs(${index}, 'zip')">
                    <i class="fas fa-file-archive"></i> ملف مضغوط شامل
                </button>
            </div>
        </div>
    `;

    // عرض النافذة المنبثقة
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تصدير المقال: ${article.filename}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${exportOptions}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // إزالة النافذة عند الإغلاق
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// تصدير مقال واحد بصيغة محددة
function exportSingleAs(index, format) {
    const article = processedResults[index];

    switch (format) {
        case 'html':
            window.ResultsExporter.exportSingleHTML(article);
            break;
        case 'text':
            window.ResultsExporter.exportSingleText(article);
            break;
        case 'zip':
            window.ResultsExporter.exportSingleZIP(article);
            break;
    }

    // إغلاق النافذة المنبثقة
    const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
    if (modal) {
        modal.hide();
    }
}

// تصدير مقال واحد
function exportSingleArticle(index) {
    const article = processedResults[index];
    if (!article) return;

    window.ResultsExporter.setResults([article]);
    window.ResultsExporter.exportToHTML([article], {
        filename: `${article.optimizedTitle || article.originalTitle}_محسن.html`
    });
}

// تصدير النتائج
function exportResults(format) {
    if (!processedResults || processedResults.length === 0) {
        UIHelper.showAlert('لا توجد نتائج للتصدير', 'warning');
        return;
    }

    window.ResultsExporter.setResults(processedResults);

    switch (format) {
        case 'html':
            window.ResultsExporter.exportToHTML();
            break;
        case 'excel':
            window.ResultsExporter.exportToExcel();
            break;
        case 'text':
            window.ResultsExporter.exportToText();
            break;
        case 'zip':
            window.ResultsExporter.exportToZIP();
            break;
        case 'all':
            window.ResultsExporter.exportAll();
            break;
        default:
            UIHelper.showAlert('صيغة التصدير غير مدعومة', 'error');
    }
}

// إنشاء HTML للتحليل المفصل
function generateDetailedAnalysisHTML(article) {
    let html = '<div class="analysis-sections">';

    // إحصائيات المقارنة
    html += `
        <div class="analysis-section mb-4">
            <h5><i class="fas fa-chart-bar text-primary"></i> إحصائيات المقارنة</h5>
            <div class="stats-comparison">
                <div class="row">
                    <div class="col-md-6">
                        <div class="stat-card original">
                            <h6>المحتوى الأصلي</h6>
                            <div class="stats-grid">
                                <div class="stat-item-small">
                                    <span class="stat-value">${article.analysis?.originalStats?.wordCount || 0}</span>
                                    <span class="stat-label">كلمة</span>
                                </div>
                                <div class="stat-item-small">
                                    <span class="stat-value">${article.analysis?.originalStats?.headings?.total || 0}</span>
                                    <span class="stat-label">عنوان</span>
                                </div>
                                <div class="stat-item-small">
                                    <span class="stat-value">${article.analysis?.originalStats?.paragraphs || 0}</span>
                                    <span class="stat-label">فقرة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stat-card optimized">
                            <h6>المحتوى المحسن</h6>
                            <div class="stats-grid">
                                <div class="stat-item-small">
                                    <span class="stat-value">${article.analysis?.optimizedStats?.wordCount || 0}</span>
                                    <span class="stat-label">كلمة</span>
                                </div>
                                <div class="stat-item-small">
                                    <span class="stat-value">${article.analysis?.optimizedStats?.headings?.total || 0}</span>
                                    <span class="stat-label">عنوان</span>
                                </div>
                                <div class="stat-item-small">
                                    <span class="stat-value">${article.analysis?.optimizedStats?.lists?.total || 0}</span>
                                    <span class="stat-label">قائمة</span>
                                </div>
                                <div class="stat-item-small">
                                    <span class="stat-value">${article.analysis?.optimizedStats?.tables || 0}</span>
                                    <span class="stat-label">جدول</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // تحليل الكلمات المفتاحية المفصل
    if (article.improvementReport?.keywordAnalysis) {
        const kw = article.improvementReport.keywordAnalysis;
        html += `
            <div class="analysis-section mb-4">
                <h5><i class="fas fa-key text-warning"></i> تحليل الكلمات المفتاحية المفصل</h5>
                <div class="keyword-detailed-analysis">
                    ${kw.primary ? `
                        <div class="primary-keyword-detailed mb-3">
                            <div class="keyword-header">
                                <span class="badge bg-primary">الكلمة المفتاحية الرئيسية</span>
                                <h6 class="mt-2">"${kw.primary.keyword}"</h6>
                            </div>
                            <div class="keyword-stats">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="stat-item-small">
                                            <span class="stat-value">${kw.primary.occurrences}</span>
                                            <span class="stat-label">مرة تكرار</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-item-small">
                                            <span class="stat-value">${kw.primary.density.toFixed(1)}%</span>
                                            <span class="stat-label">كثافة</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-item-small">
                                            <span class="stat-value">${kw.locations.length}</span>
                                            <span class="stat-label">موقع</span>
                                        </div>
                                    </div>
                                </div>
                                ${kw.locations.length > 0 ? `
                                    <div class="keyword-locations mt-2">
                                        <small><strong>المواقع:</strong> ${kw.locations.join(', ')}</small>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    ` : ''}

                    ${kw.secondary.length > 0 ? `
                        <div class="secondary-keywords-detailed">
                            <h6>الكلمات المفتاحية الثانوية</h6>
                            <div class="secondary-keywords-grid">
                                ${kw.secondary.map(s => `
                                    <div class="secondary-keyword-item">
                                        <span class="keyword-name">"${s.keyword}"</span>
                                        <span class="keyword-count">${s.occurrences} مرة</span>
                                        <span class="keyword-density">${s.density.toFixed(1)}%</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // التحسينات المطبقة بالتفصيل
    if (article.improvementReport?.detailedBreakdown) {
        const breakdown = article.improvementReport.detailedBreakdown;
        html += `
            <div class="analysis-section mb-4">
                <h5><i class="fas fa-tools text-success"></i> التحسينات المطبقة بالتفصيل</h5>
                <div class="improvements-breakdown">
                    ${breakdown.contentImprovements.length > 0 ? `
                        <div class="improvement-category mb-3">
                            <h6><i class="fas fa-edit text-primary"></i> تحسينات المحتوى</h6>
                            <ul class="improvement-list">
                                ${breakdown.contentImprovements.map(imp => `<li>${imp}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${breakdown.seoImprovements.length > 0 ? `
                        <div class="improvement-category mb-3">
                            <h6><i class="fas fa-search text-info"></i> تحسينات SEO</h6>
                            <ul class="improvement-list">
                                ${breakdown.seoImprovements.map(imp => `<li>${imp}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${breakdown.eatImprovements.length > 0 ? `
                        <div class="improvement-category mb-3">
                            <h6><i class="fas fa-award text-warning"></i> تحسينات E-A-T</h6>
                            <ul class="improvement-list">
                                ${breakdown.eatImprovements.map(imp => `<li>${imp}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${breakdown.technicalImprovements.length > 0 ? `
                        <div class="improvement-category mb-3">
                            <h6><i class="fas fa-cog text-secondary"></i> تحسينات تقنية</h6>
                            <ul class="improvement-list">
                                ${breakdown.technicalImprovements.map(imp => `<li>${imp}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // التوصيات
    if (article.improvementReport?.recommendations) {
        html += `
            <div class="analysis-section mb-4">
                <h5><i class="fas fa-lightbulb text-warning"></i> توصيات للتحسين الإضافي</h5>
                <div class="recommendations-list">
                    ${article.improvementReport.recommendations.map(rec => `
                        <div class="recommendation-item">
                            <i class="fas fa-arrow-right text-warning me-2"></i>
                            <span>${rec}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    html += '</div>';
    return html;
}

// تنسيق المحتوى الأصلي
function formatOriginalContent(content) {
    if (content.includes('<') && content.includes('>')) {
        return content;
    }

    return content
        .replace(/\n\n/g, '</p><p>')
        .replace(/\n/g, '<br>')
        .replace(/^/, '<p>')
        .replace(/$/, '</p>');
}

// مسح النتائج
function clearResults() {
    if (confirm('هل أنت متأكد من مسح جميع النتائج؟')) {
        processedResults = [];

        // إخفاء أقسام النتائج
        document.getElementById('exportControls').style.display = 'none';
        document.getElementById('resultsSummary').style.display = 'none';

        // إعادة تعيين المحتوى
        const resultsContent = document.getElementById('resultsContent');
        resultsContent.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد نتائج بعد</h5>
                <p class="text-muted">قم برفع المقالات وبدء عملية التحسين لعرض النتائج هنا</p>
            </div>
        `;

        UIHelper.showAlert('تم مسح النتائج بنجاح', 'success');
    }
}

// تحديث زر البدء عند تغيير المحتوى
document.addEventListener('input', function(e) {
    if (e.target.id === 'articleContent') {
        updateStartButton();
    }
});

// ربط النتائج مع المعالج
if (window.ArticleProcessor) {
    const originalUpdateResultsDisplay = window.ArticleProcessor.updateResultsDisplay;
    window.ArticleProcessor.updateResultsDisplay = function() {
        processedResults = this.results;
        updateResultsDisplay(this.results);
    };
}

console.log('تم تحميل الملف الرئيسي بنجاح');
