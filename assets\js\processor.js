/**
 * معالج المقالات - المعالجة الرئيسية وتحسين المقالات
 * يدير العملية الكاملة من القراءة إلى التحسين
 */

// ===== فئة معالج المقالات =====
class ArticleProcessor {
    constructor() {
        this.isProcessing = false;
        this.isPaused = false;
        this.currentProgress = 0;
        this.totalArticles = 0;
        this.processedArticles = 0;
        this.results = [];
        this.errors = [];
        this.startTime = null;
        this.pauseTime = null;
        this.estimatedTimeRemaining = 0;
    }

    // ===== بدء معالجة المقالات =====
    async processArticles(files, settings) {
        try {
            this.initializeProcessing(files.length);
            
            // قراءة جميع الملفات أولاً
            const fileContents = await this.readAllFiles(files);
            
            // استخراج المقالات من المحتوى
            const articles = this.extractArticlesFromFiles(fileContents);
            
            // تحديث العدد الإجمالي
            this.totalArticles = articles.length;
            this.updateProgressDisplay();
            
            // معالجة كل مقال
            for (let i = 0; i < articles.length; i++) {
                if (!this.isProcessing) break;
                
                // انتظار إذا كانت العملية متوقفة مؤقتاً
                await this.waitIfPaused();
                
                try {
                    await this.processSingleArticle(articles[i], i, settings);
                } catch (error) {
                    this.handleArticleError(articles[i], error, i);
                }
                
                this.processedArticles++;
                this.updateProgress();
            }
            
            this.completeProcessing();
            
        } catch (error) {
            this.handleProcessingError(error);
        }
    }

    // ===== تهيئة المعالجة =====
    initializeProcessing(fileCount) {
        this.isProcessing = true;
        this.isPaused = false;
        this.currentProgress = 0;
        this.totalArticles = fileCount;
        this.processedArticles = 0;
        this.results = [];
        this.errors = [];
        this.startTime = Date.now();
        this.pauseTime = null;
        
        // تحديث حالة التطبيق
        APP_STATE.isProcessing = true;
        APP_STATE.totalArticles = fileCount;
        APP_STATE.processedArticles = 0;
        APP_STATE.startTime = this.startTime;
        
        // إظهار شريط التقدم
        document.getElementById('progressContainer').style.display = 'block';
        this.updateProgressDisplay();
    }

    // ===== قراءة جميع الملفات =====
    async readAllFiles(files) {
        const fileContents = [];
        
        for (let i = 0; i < files.length; i++) {
            try {
                this.updateCurrentStatus(`جاري قراءة الملف: ${files[i].name}`);
                
                const content = await window.FileManager.readFile(files[i]);
                fileContents.push(content);
                
                // تحديث التقدم
                const progress = ((i + 1) / files.length) * 20; // 20% للقراءة
                this.updateProgressBar(progress, `قراءة الملفات: ${i + 1}/${files.length}`);
                
            } catch (error) {
                console.error(`خطأ في قراءة الملف ${files[i].name}:`, error);
                this.errors.push({
                    filename: files[i].name,
                    error: error.message,
                    stage: 'reading'
                });
            }
        }
        
        return fileContents;
    }

    // ===== استخراج المقالات من الملفات =====
    extractArticlesFromFiles(fileContents) {
        const articles = [];
        
        fileContents.forEach((fileContent, fileIndex) => {
            try {
                switch (fileContent.type) {
                    case 'text':
                    case 'html':
                        articles.push({
                            id: `article_${fileIndex}_0`,
                            title: this.extractTitleFromContent(fileContent.content || fileContent.textContent),
                            content: fileContent.content || fileContent.textContent,
                            originalContent: fileContent.content || fileContent.textContent,
                            filename: fileContent.filename,
                            type: fileContent.type,
                            wordCount: fileContent.wordCount,
                            charCount: fileContent.charCount
                        });
                        break;
                        
                    case 'excel':
                    case 'csv':
                        fileContent.articles.forEach((article, articleIndex) => {
                            articles.push({
                                id: `article_${fileIndex}_${articleIndex}`,
                                title: article.title,
                                content: article.content,
                                originalContent: article.content,
                                filename: fileContent.filename,
                                type: fileContent.type,
                                sheet: article.sheet,
                                row: article.row,
                                wordCount: TextHelper.countWords(article.content),
                                charCount: TextHelper.countCharacters(article.content)
                            });
                        });
                        break;
                }
            } catch (error) {
                console.error('خطأ في استخراج المقالات:', error);
                this.errors.push({
                    filename: fileContent.filename,
                    error: error.message,
                    stage: 'extraction'
                });
            }
        });
        
        return articles;
    }

    // ===== استخراج العنوان من المحتوى =====
    extractTitleFromContent(content) {
        // البحث عن عنوان في بداية النص
        const lines = content.split('\n');
        const firstLine = lines[0].trim();
        
        // إذا كان السطر الأول قصير ولا يحتوي على نقطة، فهو على الأرجح عنوان
        if (firstLine.length < 100 && !firstLine.includes('.')) {
            return firstLine;
        }
        
        // البحث عن عناوين HTML
        const h1Match = content.match(/<h1[^>]*>(.*?)<\/h1>/i);
        if (h1Match) {
            return TextHelper.extractTextFromHTML(h1Match[1]);
        }
        
        const h2Match = content.match(/<h2[^>]*>(.*?)<\/h2>/i);
        if (h2Match) {
            return TextHelper.extractTextFromHTML(h2Match[1]);
        }
        
        // إنشاء عنوان من أول 50 حرف
        return content.substring(0, 50).trim() + '...';
    }

    // ===== معالجة مقال واحد =====
    async processSingleArticle(article, index, settings) {
        try {
            this.updateCurrentStatus(`جاري معالجة: ${article.title}`);
            
            // المرحلة 1: استخراج الكلمات المفتاحية
            let keywords = null;
            try {
                this.updateCurrentStatus(`استخراج الكلمات المفتاحية: ${article.title}`);
                const keywordResult = await window.AIEngine.extractKeywords(article.content);
                if (keywordResult.success) {
                    keywords = keywordResult.keywords;
                }
            } catch (error) {
                console.warn('فشل في استخراج الكلمات المفتاحية:', error);
            }
            
            // المرحلة 2: تحسين المقال
            this.updateCurrentStatus(`تحسين المقال: ${article.title}`);
            const optimizationResult = await window.AIEngine.optimizeArticle(article.content, settings);
            
            if (!optimizationResult.success) {
                throw new Error(optimizationResult.error);
            }
            
            // المرحلة 3: معالجة النتيجة
            const processedArticle = this.processOptimizationResult(
                article, 
                optimizationResult, 
                keywords, 
                settings
            );
            
            // إضافة النتيجة
            this.results.push(processedArticle);
            
            // تحديث واجهة النتائج
            this.updateResultsDisplay();
            
        } catch (error) {
            throw new Error(`فشل في معالجة المقال "${article.title}": ${error.message}`);
        }
    }

    // ===== معالجة نتيجة التحسين =====
    processOptimizationResult(originalArticle, optimizationResult, keywords, settings) {
        const optimizedContent = optimizationResult.content;
        
        // تحليل المحتوى المحسن
        const analysis = this.analyzeOptimizedContent(
            originalArticle.content, 
            optimizedContent, 
            keywords
        );
        
        // إنشاء تقرير التحسين
        const improvementReport = this.generateImprovementReport(
            originalArticle, 
            optimizedContent, 
            analysis, 
            settings
        );
        
        return {
            id: originalArticle.id,
            originalTitle: originalArticle.title,
            optimizedTitle: this.extractOptimizedTitle(optimizedContent),
            originalContent: originalArticle.originalContent,
            optimizedContent: optimizedContent,
            filename: originalArticle.filename,
            type: originalArticle.type,
            keywords: keywords,
            analysis: analysis,
            improvementReport: improvementReport,
            usage: optimizationResult.usage,
            processedAt: new Date().toISOString(),
            settings: settings
        };
    }

    // ===== استخراج العنوان المحسن =====
    extractOptimizedTitle(optimizedContent) {
        // البحث عن عنوان H1 أو H2
        const h1Match = optimizedContent.match(/<h1[^>]*>(.*?)<\/h1>/i);
        if (h1Match) {
            return TextHelper.extractTextFromHTML(h1Match[1]);
        }
        
        const h2Match = optimizedContent.match(/<h2[^>]*>(.*?)<\/h2>/i);
        if (h2Match) {
            return TextHelper.extractTextFromHTML(h2Match[1]);
        }
        
        // البحث في بداية النص
        const textContent = TextHelper.extractTextFromHTML(optimizedContent);
        const lines = textContent.split('\n');
        return lines[0].trim() || 'عنوان محسن';
    }

    // ===== تحليل المحتوى المحسن =====
    analyzeOptimizedContent(originalContent, optimizedContent, keywords) {
        const originalText = TextHelper.extractTextFromHTML(originalContent);
        const optimizedText = TextHelper.extractTextFromHTML(optimizedContent);
        
        return {
            originalStats: {
                wordCount: TextHelper.countWords(originalText),
                charCount: TextHelper.countCharacters(originalText),
                paragraphs: originalText.split('\n\n').length
            },
            optimizedStats: {
                wordCount: TextHelper.countWords(optimizedText),
                charCount: TextHelper.countCharacters(optimizedText),
                paragraphs: optimizedText.split('\n\n').length,
                headings: this.countHeadings(optimizedContent),
                lists: this.countLists(optimizedContent),
                tables: this.countTables(optimizedContent)
            },
            improvements: {
                wordIncrease: TextHelper.countWords(optimizedText) - TextHelper.countWords(originalText),
                structureAdded: this.hasImprovedStructure(originalContent, optimizedContent),
                seoOptimized: this.hasSEOOptimizations(optimizedContent),
                keywordDensity: keywords ? this.calculateKeywordDensity(optimizedText, keywords) : null
            }
        };
    }

    // ===== عد العناوين =====
    countHeadings(content) {
        const headings = content.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi) || [];
        return {
            total: headings.length,
            h1: (content.match(/<h1[^>]*>.*?<\/h1>/gi) || []).length,
            h2: (content.match(/<h2[^>]*>.*?<\/h2>/gi) || []).length,
            h3: (content.match(/<h3[^>]*>.*?<\/h3>/gi) || []).length
        };
    }

    // ===== عد القوائم =====
    countLists(content) {
        return {
            unordered: (content.match(/<ul[^>]*>.*?<\/ul>/gi) || []).length,
            ordered: (content.match(/<ol[^>]*>.*?<\/ol>/gi) || []).length,
            total: (content.match(/<[uo]l[^>]*>.*?<\/[uo]l>/gi) || []).length
        };
    }

    // ===== عد الجداول =====
    countTables(content) {
        return (content.match(/<table[^>]*>.*?<\/table>/gi) || []).length;
    }

    // ===== التحقق من تحسين البنية =====
    hasImprovedStructure(original, optimized) {
        const originalHeadings = this.countHeadings(original).total;
        const optimizedHeadings = this.countHeadings(optimized).total;
        
        return optimizedHeadings > originalHeadings;
    }

    // ===== التحقق من تحسينات SEO =====
    hasSEOOptimizations(content) {
        const checks = {
            hasTitle: /<h[1-2][^>]*>.*?<\/h[1-2]>/i.test(content),
            hasStructure: this.countHeadings(content).total > 0,
            hasLists: this.countLists(content).total > 0,
            hasFormatting: /<(strong|b|em|i)[^>]*>.*?<\/(strong|b|em|i)>/i.test(content)
        };
        
        return Object.values(checks).filter(Boolean).length >= 3;
    }

    // ===== حساب كثافة الكلمات المفتاحية =====
    calculateKeywordDensity(text, keywords) {
        if (!keywords || !keywords.primary) return 0;
        
        const totalWords = TextHelper.countWords(text);
        const keywordOccurrences = (text.toLowerCase().match(new RegExp(keywords.primary.toLowerCase(), 'g')) || []).length;
        
        return (keywordOccurrences / totalWords) * 100;
    }

    // ===== إنشاء تقرير التحسين =====
    generateImprovementReport(originalArticle, optimizedContent, analysis, settings) {
        const improvements = [];
        
        // تحسينات الكلمات
        if (analysis.improvements.wordIncrease > 0) {
            improvements.push(`تم إضافة ${analysis.improvements.wordIncrease} كلمة لإثراء المحتوى`);
        }
        
        // تحسينات البنية
        if (analysis.optimizedStats.headings.total > 0) {
            improvements.push(`تم إضافة ${analysis.optimizedStats.headings.total} عنوان لتحسين البنية`);
        }
        
        if (analysis.optimizedStats.lists.total > 0) {
            improvements.push(`تم إضافة ${analysis.optimizedStats.lists.total} قائمة منظمة`);
        }
        
        if (analysis.optimizedStats.tables > 0) {
            improvements.push(`تم إضافة ${analysis.optimizedStats.tables} جدول لتنظيم المعلومات`);
        }
        
        // تحسينات SEO
        if (analysis.improvements.seoOptimized) {
            improvements.push('تم تحسين المقال لمحركات البحث');
        }
        
        if (analysis.improvements.keywordDensity) {
            improvements.push(`كثافة الكلمات المفتاحية: ${analysis.improvements.keywordDensity.toFixed(1)}%`);
        }
        
        return {
            improvements: improvements,
            summary: `تم تحسين المقال بنجاح مع إضافة ${analysis.improvements.wordIncrease} كلمة و ${analysis.optimizedStats.headings.total} عنوان`,
            score: this.calculateImprovementScore(analysis),
            recommendations: this.generateRecommendations(analysis, settings)
        };
    }

    // ===== حساب نقاط التحسين =====
    calculateImprovementScore(analysis) {
        let score = 0;
        
        // نقاط للكلمات المضافة
        score += Math.min(analysis.improvements.wordIncrease / 100, 20);
        
        // نقاط للعناوين
        score += Math.min(analysis.optimizedStats.headings.total * 5, 25);
        
        // نقاط للقوائم
        score += Math.min(analysis.optimizedStats.lists.total * 3, 15);
        
        // نقاط للجداول
        score += Math.min(analysis.optimizedStats.tables * 5, 20);
        
        // نقاط لتحسينات SEO
        if (analysis.improvements.seoOptimized) score += 20;
        
        return Math.min(Math.round(score), 100);
    }

    // ===== إنشاء التوصيات =====
    generateRecommendations(analysis, settings) {
        const recommendations = [];
        
        if (analysis.optimizedStats.headings.total < 3) {
            recommendations.push('يُنصح بإضافة المزيد من العناوين الفرعية');
        }
        
        if (analysis.optimizedStats.lists.total === 0) {
            recommendations.push('يمكن إضافة قوائم نقطية لتحسين القراءة');
        }
        
        if (analysis.improvements.keywordDensity && analysis.improvements.keywordDensity < 1) {
            recommendations.push('يمكن زيادة كثافة الكلمات المفتاحية قليلاً');
        }
        
        return recommendations;
    }

    // ===== انتظار إذا كانت العملية متوقفة =====
    async waitIfPaused() {
        while (this.isPaused && this.isProcessing) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    // ===== تحديث التقدم =====
    updateProgress() {
        const percentage = (this.processedArticles / this.totalArticles) * 100;
        this.currentProgress = percentage;
        
        // تحديث شريط التقدم
        this.updateProgressBar(percentage, `${this.processedArticles}/${this.totalArticles} مقال`);
        
        // تحديث الإحصائيات
        document.getElementById('completedCount').textContent = this.processedArticles;
        document.getElementById('remainingCount').textContent = this.totalArticles - this.processedArticles;
        
        // حساب الوقت المتبقي
        this.calculateEstimatedTime();
    }

    // ===== تحديث شريط التقدم =====
    updateProgressBar(percentage, text) {
        const progressBar = document.getElementById('mainProgressBar');
        const progressText = document.getElementById('progressText');
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }
        
        if (progressText) {
            progressText.textContent = text || Math.round(percentage) + '%';
        }
    }

    // ===== تحديث الحالة الحالية =====
    updateCurrentStatus(status) {
        const statusElement = document.getElementById('currentStatus');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    // ===== تحديث عرض التقدم =====
    updateProgressDisplay() {
        document.getElementById('completedCount').textContent = this.processedArticles;
        document.getElementById('remainingCount').textContent = this.totalArticles - this.processedArticles;
    }

    // ===== حساب الوقت المتبقي =====
    calculateEstimatedTime() {
        if (this.processedArticles === 0) return;
        
        const elapsedTime = Date.now() - this.startTime;
        const averageTimePerArticle = elapsedTime / this.processedArticles;
        const remainingArticles = this.totalArticles - this.processedArticles;
        const estimatedTimeRemaining = remainingArticles * averageTimePerArticle;
        
        this.estimatedTimeRemaining = estimatedTimeRemaining;
        
        const estimatedTimeElement = document.getElementById('estimatedTime');
        if (estimatedTimeElement) {
            estimatedTimeElement.textContent = Formatter.formatTime(estimatedTimeRemaining / 1000);
        }
    }

    // ===== تحديث عرض النتائج =====
    updateResultsDisplay() {
        // سيتم تنفيذها في ملف النتائج
        if (window.ResultsManager) {
            window.ResultsManager.updateDisplay(this.results);
        }
    }

    // ===== معالجة خطأ المقال =====
    handleArticleError(article, error, index) {
        console.error(`خطأ في معالجة المقال ${index + 1}:`, error);
        
        this.errors.push({
            articleId: article.id,
            title: article.title,
            filename: article.filename,
            error: error.message,
            stage: 'processing',
            index: index
        });
        
        UIHelper.showAlert(`خطأ في معالجة المقال: ${article.title}`, 'error');
    }

    // ===== معالجة خطأ المعالجة العامة =====
    handleProcessingError(error) {
        console.error('خطأ في المعالجة العامة:', error);
        
        this.isProcessing = false;
        this.updateCurrentStatus('توقفت المعالجة بسبب خطأ');
        
        UIHelper.showAlert(`خطأ في المعالجة: ${error.message}`, 'error');
    }

    // ===== إكمال المعالجة =====
    completeProcessing() {
        this.isProcessing = false;
        this.updateCurrentStatus('تم الانتهاء من المعالجة');
        this.updateProgressBar(100, 'مكتمل');
        
        // إظهار ملخص النتائج
        const successCount = this.results.length;
        const errorCount = this.errors.length;
        
        UIHelper.showAlert(
            `تم الانتهاء من المعالجة! نجح: ${successCount}، فشل: ${errorCount}`, 
            successCount > 0 ? 'success' : 'warning'
        );
        
        // التبديل إلى تبويب النتائج
        if (successCount > 0) {
            document.getElementById('results-tab').click();
        }
    }

    // ===== إيقاف المعالجة =====
    stopProcessing() {
        this.isProcessing = false;
        this.isPaused = false;
        this.updateCurrentStatus('تم إيقاف المعالجة');
    }

    // ===== إيقاف مؤقت =====
    pauseProcessing() {
        this.isPaused = !this.isPaused;
        
        if (this.isPaused) {
            this.pauseTime = Date.now();
            this.updateCurrentStatus('متوقف مؤقتاً');
        } else {
            if (this.pauseTime) {
                this.startTime += Date.now() - this.pauseTime;
            }
            this.updateCurrentStatus('جاري المعالجة');
        }
    }

    // ===== الحصول على النتائج =====
    getResults() {
        return {
            results: this.results,
            errors: this.errors,
            stats: {
                total: this.totalArticles,
                processed: this.processedArticles,
                successful: this.results.length,
                failed: this.errors.length,
                processingTime: Date.now() - this.startTime
            }
        };
    }
}

// ===== إنشاء مثيل عام =====
window.ArticleProcessor = new ArticleProcessor();

console.log('تم تحميل معالج المقالات بنجاح');
