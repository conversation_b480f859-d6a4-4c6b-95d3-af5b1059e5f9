# أداة تحسين المقالات للسيو بالذكاء الصناعي

## 📋 نظرة عامة

أداة احترافية متقدمة لتحسين المقالات لمحركات البحث (SEO) باستخدام الذكاء الصناعي. تستخدم الأداة تقنيات Gemini AI المتطورة لتحليل وتحسين المحتوى بشكل احترافي وفقاً لأحدث معايير تحسين محركات البحث.

## ✨ المميزات الرئيسية

### 🤖 تحسين ذكي متقدم
- **تحسين SEO احترافي**: تطبيق أحدث معايير تحسين محركات البحث
- **تحسين لغوي**: تصحيح الأخطاء النحوية والإملائية وتحسين التراكيب
- **اختيار الكلمات المفتاحية**: استخراج وتوزيع الكلمات المفتاحية الأنسب
- **صياغة العناوين**: إنشاء عناوين جذابة ومحسنة للسيو

### 📁 دعم ملفات متعددة
- **ملفات نصية**: TXT, MD
- **ملفات HTML**: HTML, HTM
- **ملفات Excel**: XLSX, XLS
- **ملفات CSV**: CSV
- **إدخال مباشر**: إضافة المقالات يدوياً

### ⚙️ خيارات تحسين متقدمة
- **أنواع الصياغة**: احترافي، ودي، تخويف، دعائي، أكاديمي، إخباري، قانوني، طبي
- **اللهجات**: فصحى، خليجي، مصري، ليبي، سوداني
- **الخطوط**: Tajawal، Cairo، Amiri، Noto Sans Arabic
- **تنسيق العناوين**: تدرج لوني، خط سفلي، خط جانبي، ظل، تظليل
- **كثافة الأيقونات**: بدون، قليلة، متوسطة، كثيفة
- **أنماط الجداول**: حديث، كلاسيكي، بسيط، ملون
- **التحسينات البصرية**: قوائم نقطية محسنة، جداول احترافية، اقتباسات أنيقة
- **إعدادات المحتوى**: كثافة الكلمات المفتاحية، طول الفقرات، نوع العناوين

### 📊 تقارير تفصيلية متقدمة
- **تحليل شامل**: إحصائيات مفصلة للتحسينات المطبقة
- **معايير SEO**: تقييم الكلمات المفتاحية، البنية، والتحسينات التقنية
- **معايير E-E-A-T**: تقييم الخبرة، السلطة، الثقة، والتجربة
- **نقاط التحسين**: تقييم رقمي شامل لجودة التحسين (0-100%)
- **توصيات متقدمة**: اقتراحات مبنية على أحدث معايير Google
- **مقارنة تفاعلية**: عرض الفروقات بين النص الأصلي والمحسن
- **تقارير فردية**: تحليل مفصل لكل مقال على حدة

### 📤 تصدير شامل ومنظم
- **HTML**: ملفات HTML منسقة مع CSS مدمج ومحرك بحث تفاعلي
- **Excel**: جداول منظمة مع تحليلات مفصلة وإحصائيات
- **نص**: ملفات نصية منظمة وسهلة القراءة
- **ZIP شامل**: ملف مضغوط منظم يحتوي على:
  - مجلد HTML مع الملف الشامل
  - مجلد النصوص مع جميع المقالات
  - مجلد التقارير مع التحليلات المفصلة
  - مجلد المقالات الفردية (كل مقال في مجلد منفصل)
  - ملف README شامل للتوضيح

## 🚀 التقنيات المستخدمة

- **HTML5**: هيكل الصفحة الأساسي
- **CSS3**: التصميم والتنسيق المتقدم
- **Bootstrap 5**: إطار العمل للتصميم المتجاوب
- **JavaScript ES6+**: منطق التطبيق والتفاعل
- **Gemini AI API**: محرك الذكاء الصناعي
- **Font Awesome**: الأيقونات
- **Google Fonts**: خطوط Cairo و Tajawal

## 📦 هيكل المشروع

```
Articles SEO Edit/
├── index.html                 # الصفحة الرئيسية
├── assets/
│   ├── css/
│   │   └── main.css          # ملف التصميم الرئيسي
│   └── js/
│       ├── config.js         # إعدادات التطبيق
│       ├── utils.js          # دوال مساعدة عامة
│       ├── fileManager.js    # إدارة الملفات
│       ├── aiEngine.js       # محرك الذكاء الصناعي
│       ├── processor.js      # معالج المقالات
│       ├── exporter.js       # مصدر النتائج
│       └── main.js           # الملف الرئيسي
└── README.md                 # ملف التوثيق
```

## 🛠️ التثبيت والاستخدام

### متطلبات النظام
- متصفح ويب حديث يدعم ES6+
- اتصال بالإنترنت لتحميل المكتبات الخارجية
- مفاتيح Gemini API (مضمنة في الكود)

### خطوات التشغيل
1. **تحميل الملفات**: قم بتحميل جميع ملفات المشروع
2. **فتح الأداة**: افتح ملف `index.html` في المتصفح
3. **رفع المقالات**: استخدم تبويب "رفع المقالات" لإضافة المحتوى
4. **تخصيص الإعدادات**: اذهب لتبويب "خيارات التحسين" لتخصيص الإعدادات
5. **بدء المعالجة**: انقر على "بدء عملية التحسين"
6. **عرض النتائج**: راجع النتائج في تبويب "النتائج والتقارير"
7. **تصدير النتائج**: استخدم أزرار التصدير للحصول على الملفات النهائية

## 🎯 كيفية الاستخدام

### 1. رفع المقالات
- **رفع ملفات**: اسحب الملفات أو انقر لاختيارها
- **إدخال مباشر**: الصق المحتوى مباشرة في النموذج
- **ملفات Excel/CSV**: سيتم استخراج المقالات تلقائياً من الأعمدة

### 2. تخصيص الإعدادات
- **خيارات التحسين**: فعّل/عطّل التحسينات المطلوبة
- **نوع الصياغة**: اختر الأسلوب المناسب لجمهورك
- **اللهجة**: حدد اللهجة المفضلة
- **إعدادات المحتوى**: اضبط كثافة الكلمات المفتاحية وطول الفقرات

### 3. مراقبة التقدم
- **شريط التقدم**: متابعة نسبة الإنجاز
- **إحصائيات مباشرة**: عدد المقالات المكتملة والمتبقية
- **تحكم في العملية**: إيقاف مؤقت أو إيقاف كامل

### 4. مراجعة النتائج
- **عرض المقالات**: مراجعة المحتوى المحسن
- **تقارير التحسين**: تحليل مفصل للتحسينات المطبقة
- **مقارنة**: عرض الفروقات بين النص الأصلي والمحسن

## 🔧 الإعدادات المتقدمة

### إدارة مفاتيح API
الأداة تستخدم نظام تدوير ذكي لمفاتيح Gemini API لضمان الاستمرارية:
- **15 مفتاح API**: للتعامل مع الأحمال الكبيرة
- **تدوير تلقائي**: تبديل المفاتيح عند الوصول للحدود
- **إدارة الأخطاء**: تجاوز المفاتيح المعطلة تلقائياً

### تخصيص البرومبت
يمكن تعديل قوالب البرومبت في ملف `config.js`:
- **برومبت تحسين SEO**: للتحسين العام
- **برومبت التحسين اللغوي**: للتصحيح اللغوي
- **برومبت استخراج الكلمات المفتاحية**: لتحليل المحتوى

## 📈 الأداء والحدود

### حدود المعالجة
- **حجم الملف الواحد**: 10 ميجابايت كحد أقصى
- **عدد الملفات**: 50 ملف كحد أقصى
- **طول المقال**: يتم تقسيم المقالات الطويلة تلقائياً

### تحسين الأداء
- **معالجة متوازية**: استخدام عدة مفاتيح API
- **تقسيم ذكي**: تجزئة المحتوى الكبير
- **ذاكرة التخزين المؤقت**: حفظ الإعدادات محلياً

## 🛡️ الأمان والخصوصية

- **معالجة محلية**: لا يتم حفظ المحتوى على خوادم خارجية
- **تشفير الاتصال**: جميع الطلبات مشفرة عبر HTTPS
- **عدم تخزين البيانات**: المحتوى يُعالج ويُحذف فوراً

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في تحميل الملف**
- تأكد من نوع الملف المدعوم
- تحقق من حجم الملف (أقل من 10 ميجابايت)

**فشل في الاتصال بـ API**
- تحقق من الاتصال بالإنترنت
- انتظر قليلاً وأعد المحاولة (قد تكون المفاتيح مؤقتاً محدودة)

**بطء في المعالجة**
- قلل عدد المقالات في الدفعة الواحدة
- تأكد من استقرار الاتصال بالإنترنت

## 🤝 المساهمة والتطوير

### إضافة مميزات جديدة
1. **إضافة لغات جديدة**: تعديل ملف `config.js`
2. **تحسين البرومبت**: تطوير قوالب جديدة
3. **صيغ تصدير إضافية**: إضافة دعم لصيغ جديدة

### تحسينات مقترحة
- [ ] دعم تصدير PDF كامل
- [ ] إضافة المزيد من اللغات
- [ ] تحسين خوارزمية تقسيم المحتوى
- [ ] إضافة نظام قوالب جاهزة
- [ ] دعم الصور والوسائط

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع قسم "استكشاف الأخطاء" أعلاه
- تحقق من وحدة التحكم في المتصفح للأخطاء التقنية
- تأكد من تحديث المتصفح لأحدث إصدار

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**تم تطوير هذه الأداة باستخدام أحدث تقنيات الذكاء الصناعي لتوفير أفضل تجربة في تحسين المحتوى العربي لمحركات البحث.**
