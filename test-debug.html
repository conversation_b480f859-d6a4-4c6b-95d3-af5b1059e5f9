<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للأداة</title>
</head>
<body>
    <h1>اختبار سريع</h1>
    <div id="test-results"></div>
    
    <script src="assets/js/config.js"></script>
    <script src="assets/js/utils.js"></script>
    <script src="assets/js/fileManager.js"></script>
    <script src="assets/js/aiEngine.js"></script>
    <script src="assets/js/processor.js"></script>
    <script src="assets/js/exporter.js"></script>
    
    <script>
        // اختبار سريع للتأكد من أن جميع الكلاسات تعمل
        console.log('اختبار الكلاسات:');
        console.log('FileManager:', typeof window.FileManager);
        console.log('AIEngine:', typeof window.AIEngine);
        console.log('ArticleProcessor:', typeof window.ArticleProcessor);
        console.log('ResultsExporter:', typeof window.ResultsExporter);
        
        // اختبار دالة بسيطة
        if (window.FileManager && typeof window.FileManager.validateFile === 'function') {
            console.log('✅ FileManager يعمل بشكل صحيح');
        } else {
            console.log('❌ مشكلة في FileManager');
        }
        
        if (window.AIEngine && typeof window.AIEngine.getUsageStats === 'function') {
            console.log('✅ AIEngine يعمل بشكل صحيح');
        } else {
            console.log('❌ مشكلة في AIEngine');
        }
        
        if (window.ArticleProcessor && typeof window.ArticleProcessor.processArticles === 'function') {
            console.log('✅ ArticleProcessor يعمل بشكل صحيح');
        } else {
            console.log('❌ مشكلة في ArticleProcessor');
        }
        
        if (window.ResultsExporter && typeof window.ResultsExporter.exportToHTML === 'function') {
            console.log('✅ ResultsExporter يعمل بشكل صحيح');
        } else {
            console.log('❌ مشكلة في ResultsExporter');
        }
        
        document.getElementById('test-results').innerHTML = 'تحقق من وحدة التحكم (Console) لرؤية نتائج الاختبار';
    </script>
</body>
</html>
