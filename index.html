<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة تحسين المقالات للسيو بالذكاء الصناعي - محسن SEO احترافي</title>
    <meta name="description" content="أداة احترافية لتحسين المقالات للسيو باستخدام الذكاء الصناعي. تحسين لغوي ومحتوى وكلمات مفتاحية بمعايير حديثة لزيادة ترتيب موقعك في محركات البحث">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/main.css">
    
    <!-- Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "أداة تحسين المقالات للسيو بالذكاء الصناعي",
        "description": "أداة احترافية لتحسين المقالات للسيو باستخدام الذكاء الصناعي",
        "url": "https://example.com",
        "applicationCategory": "SEO Tool",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "author": {
            "@type": "Organization",
            "name": "SEO AI Tools"
        }
    }
    </script>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>جاري تحميل الأداة...</h3>
            <p>يرجى الانتظار</p>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container-fluid" id="mainApp" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="app-title">
                            <i class="fas fa-robot text-primary"></i>
                            أداة تحسين المقالات للسيو بالذكاء الصناعي
                        </h1>
                        <p class="app-subtitle">محسن احترافي للمقالات باستخدام أحدث تقنيات الذكاء الصناعي</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-outline-primary" id="settingsBtn">
                            <i class="fas fa-cog"></i> الإعدادات
                        </button>
                        <button class="btn btn-outline-success" id="helpBtn">
                            <i class="fas fa-question-circle"></i> المساعدة
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="container">
                <!-- Progress Bar -->
                <div class="progress-container" id="progressContainer" style="display: none;">
                    <div class="progress-header">
                        <h4 id="progressTitle">جاري معالجة المقالات...</h4>
                        <div class="progress-controls">
                            <button class="btn btn-warning btn-sm" id="pauseBtn">
                                <i class="fas fa-pause"></i> إيقاف مؤقت
                            </button>
                            <button class="btn btn-danger btn-sm" id="stopBtn">
                                <i class="fas fa-stop"></i> إيقاف
                            </button>
                        </div>
                    </div>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             id="mainProgressBar" role="progressbar" style="width: 0%">
                            <span id="progressText">0%</span>
                        </div>
                    </div>
                    <div class="progress-details">
                        <div class="row">
                            <div class="col-md-3">
                                <small class="text-muted">المقالات المكتملة:</small>
                                <div class="fw-bold" id="completedCount">0</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">المقالات المتبقية:</small>
                                <div class="fw-bold" id="remainingCount">0</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">الوقت المتبقي:</small>
                                <div class="fw-bold" id="estimatedTime">--</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">الحالة:</small>
                                <div class="fw-bold" id="currentStatus">جاري المعالجة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Tabs -->
                <div class="card main-card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="mainTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="upload-tab" data-bs-toggle="tab" 
                                        data-bs-target="#upload-pane" type="button" role="tab">
                                    <i class="fas fa-upload"></i> رفع المقالات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="options-tab" data-bs-toggle="tab" 
                                        data-bs-target="#options-pane" type="button" role="tab">
                                    <i class="fas fa-sliders-h"></i> خيارات التحسين
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="results-tab" data-bs-toggle="tab" 
                                        data-bs-target="#results-pane" type="button" role="tab">
                                    <i class="fas fa-chart-line"></i> النتائج والتقارير
                                </button>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card-body">
                        <div class="tab-content" id="mainTabContent">
                            <!-- Upload Tab -->
                            <div class="tab-pane fade show active" id="upload-pane" role="tabpanel">
                                <div id="uploadSection"></div>
                            </div>
                            
                            <!-- Options Tab -->
                            <div class="tab-pane fade" id="options-pane" role="tabpanel">
                                <div id="optionsSection"></div>
                            </div>
                            
                            <!-- Results Tab -->
                            <div class="tab-pane fade" id="results-pane" role="tabpanel">
                                <div id="resultsSection"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <p>&copy; 2024 أداة تحسين المقالات للسيو. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <p>مدعوم بتقنيات الذكاء الصناعي المتقدمة</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Modals will be loaded here -->
    <div id="modalsContainer"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/config.js"></script>
    <script src="assets/js/utils.js"></script>
    <script src="assets/js/fileManager.js"></script>
    <script src="assets/js/aiEngine.js"></script>
    <script src="assets/js/processor.js"></script>
    <script src="assets/js/exporter.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
