/**
 * ملف التكوين الأساسي لأداة تحسين المقالات للسيو
 * يحتوي على جميع الإعدادات والثوابت المطلوبة
 */

// ===== إعدادات Gemini API =====
const GEMINI_CONFIG = {
    // مفاتيح API متعددة للتدوير
    API_KEYS: [
        'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
        'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
        'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
        'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
        'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
        'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
        'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
        'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
        'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
        'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
        'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
        'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
        'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
        'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
        'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
    ],
    
    // رابط API الأساسي
    BASE_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
    
    // حدود التوكن والطلبات
    MAX_TOKENS_PER_REQUEST: 30000,
    MAX_REQUESTS_PER_MINUTE: 60,
    MAX_REQUESTS_PER_DAY: 1500,
    
    // إعدادات التأخير بين الطلبات
    REQUEST_DELAY: 1000, // ميلي ثانية
    RETRY_DELAY: 2000,   // ميلي ثانية
    MAX_RETRIES: 3,
    
    // فهرس المفتاح الحالي
    currentKeyIndex: 0,
    
    // إحصائيات الاستخدام
    usage: {
        requestsToday: 0,
        requestsThisMinute: 0,
        lastResetTime: new Date().toDateString(),
        lastMinuteReset: Date.now()
    }
};

// ===== الإعدادات الافتراضية للتحسين =====
const DEFAULT_SETTINGS = {
    // خيارات التحسين الأساسية
    optimization: {
        languageImprovement: true,
        simplifyTerms: false,
        addBulletPoints: true,
        addTables: true,
        createNewTitle: true,
        embedCSS: true,
        enrichContent: false,
        addExternalSources: false
    },
    
    // نوع الصياغة
    writingStyle: 'professional', // professional, friendly, scary, promotional, etc.
    
    // لهجة الصياغة
    dialect: 'formal', // formal, gulf, egyptian, libyan, sudanese, american, british
    
    // ألوان وستايلات
    colorScheme: 'blue-orange-red', // blue, blue-orange-red, black
    
    // الرموز والأيقونات
    icons: {
        enabled: true,
        density: 'medium', // none, low, medium, high
        location: 'all' // headers, tables, all
    },

    // إعدادات الخطوط والتنسيق
    styling: {
        fontFamily: 'tajawal', // tajawal, cairo, amiri, noto
        headingColor: '#2563eb', // لون العناوين الرئيسية
        subheadingColor: '#1e40af', // لون العناوين الفرعية
        headingStyle: 'gradient', // gradient, underline, border-left, shadow, highlight
        tableStyle: 'modern', // modern, classic, minimal, colorful
        listStyle: 'enhanced', // enhanced, simple, boxed
        quoteStyle: 'modern' // modern, classic, minimal
    },
    
    // التشكيل
    diacritics: false,
    
    // إعدادات المحتوى
    content: {
        startWithH1: false, // true for H1, false for H2
        minWordsPerParagraph: 100,
        keywordDensity: 1, // نسبة مئوية
        minMainHeadings: 0, // 0 = حسب الحاجة
        minSubHeadings: 0   // 0 = حسب الحاجة
    },
    
    // تعليمات إضافية
    additionalInstructions: '',
    
    // مصطلحات مخصصة
    customTerms: {
        replacements: {}, // كلمة قديمة: كلمة جديدة
        additions: [],    // مصطلحات للإضافة
        references: []    // مراجع للإشارة إليها
    }
};

// ===== أنواع الملفات المدعومة =====
const SUPPORTED_FILE_TYPES = {
    text: ['.txt', '.md'],
    html: ['.html', '.htm'],
    excel: ['.xlsx', '.xls', '.csv'],
    maxFileSize: 10 * 1024 * 1024, // 10 ميجابايت
    maxFiles: 50
};

// ===== رسائل النظام =====
const MESSAGES = {
    ar: {
        // رسائل النجاح
        success: {
            fileUploaded: 'تم رفع الملف بنجاح',
            settingsSaved: 'تم حفظ الإعدادات بنجاح',
            processingComplete: 'تم الانتهاء من معالجة جميع المقالات',
            exportComplete: 'تم تصدير النتائج بنجاح'
        },
        
        // رسائل الخطأ
        error: {
            fileTooBig: 'حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت',
            unsupportedFile: 'نوع الملف غير مدعوم',
            tooManyFiles: 'عدد الملفات كبير جداً. الحد الأقصى 50 ملف',
            apiError: 'خطأ في الاتصال بخدمة الذكاء الصناعي',
            processingError: 'خطأ في معالجة المقال',
            networkError: 'خطأ في الاتصال بالإنترنت'
        },
        
        // رسائل التحذير
        warning: {
            largeFile: 'الملف كبير وقد يستغرق وقتاً أطول للمعالجة',
            manyFiles: 'عدد كبير من الملفات. قد تستغرق العملية وقتاً طويلاً',
            apiLimitReached: 'تم الوصول لحد الطلبات. سيتم التبديل لمفتاح آخر'
        },
        
        // رسائل المعلومات
        info: {
            processing: 'جاري معالجة المقال...',
            uploading: 'جاري رفع الملف...',
            analyzing: 'جاري تحليل المحتوى...',
            optimizing: 'جاري تحسين المقال...',
            exporting: 'جاري تصدير النتائج...'
        }
    }
};

// ===== قوالب البرومبت =====
const PROMPT_TEMPLATES = {
    // برومبت تحسين SEO
    seoOptimization: `
أنت خبير SEO متخصص في تحسين المحتوى العربي لمحركات البحث. مهمتك تحسين المقال التالي وفقاً لأحدث معايير Google وتطبيق مبادئ E-E-A-T.

المقال الأصلي:
{originalContent}

متطلبات التحسين الإلزامية:
1. تحسين SEO متقدم:
   - استهداف كلمة مفتاحية رئيسية واضحة
   - توزيع الكلمات المفتاحية بكثافة {keywordDensity}% طبيعياً
   - استخدام LSI keywords ومرادفات
   - تحسين العنوان الرئيسي (30-60 حرف)
   - إنشاء meta description مثالي (120-160 حرف)

2. تطبيق معايير E-E-A-T:
   - إظهار الخبرة (Experience) في الموضوع
   - إثبات السلطة (Authority) بمعلومات دقيقة
   - بناء الثقة (Trust) بمصادر موثوقة
   - إضافة الخبرة العملية (Expertise)

3. بنية المحتوى:
   - عناوين H2, H3 محسنة للسيو
   - فقرات لا تقل عن {minWordsPerParagraph} كلمة
   - {additionalRequirements}

4. التنسيق والأسلوب:
   - نوع الصياغة: {writingStyle}
   - اللهجة: {dialect}
   - خط: {fontFamily}
   - تنسيق العناوين: {headingStyle}

تعليمات مهمة:
- أرجع المحتوى المحسن فقط بصيغة HTML نظيف
- لا تضع أي مقدمات أو تعليقات قبل أو بعد المحتوى
- لا تستخدم كلمات مثل "بالتأكيد" أو "إليك المقال"
- ابدأ مباشرة بالمحتوى المحسن
- استخدم HTML5 semantic tags
`,

    // برومبت التحسين اللغوي
    languageImprovement: `
أنت خبير في اللغة العربية والتحرير الاحترافي.
مهمتك تحسين المقال التالي لغوياً مع الحفاظ على المعنى الأصلي.

المقال:
{content}

متطلبات التحسين اللغوي:
- تصحيح الأخطاء النحوية والإملائية
- تحسين التراكيب اللغوية
- توحيد المصطلحات
- تحسين التدفق والربط بين الفقرات
- {additionalRequirements}

اللهجة المطلوبة: {dialect}
إضافة التشكيل: {diacritics}
`,

    // برومبت استخراج الكلمات المفتاحية
    keywordExtraction: `
حلل المحتوى التالي واستخرج أفضل الكلمات المفتاحية المناسبة لتحسين محركات البحث:

المحتوى:
{content}

المطلوب:
- استخراج 10-15 كلمة مفتاحية رئيسية
- تحديد الكلمة المفتاحية الأساسية الأنسب
- اقتراح كلمات مفتاحية طويلة الذيل
- تقديم مرادفات وكلمات ذات صلة

أرجع النتيجة بصيغة JSON منظمة.
`
};

// ===== إعدادات التصدير =====
const EXPORT_SETTINGS = {
    formats: {
        html: {
            enabled: true,
            includeCSS: true,
            includeSearch: true
        },
        excel: {
            enabled: true,
            includeAnalysis: true
        },
        text: {
            enabled: true,
            encoding: 'utf-8'
        },
        pdf: {
            enabled: true,
            rtlSupport: true,
            fonts: ['Cairo', 'Tajawal']
        },
        zip: {
            enabled: true,
            includeAll: true
        }
    }
};

// ===== متغيرات النظام =====
let APP_STATE = {
    isProcessing: false,
    isPaused: false,
    currentProgress: 0,
    totalArticles: 0,
    processedArticles: 0,
    errors: [],
    results: [],
    startTime: null,
    estimatedTime: null
};

// ===== دوال المساعدة للتكوين =====
const ConfigHelper = {
    // حفظ الإعدادات في التخزين المحلي
    saveSettings: function(settings) {
        try {
            localStorage.setItem('seoToolSettings', JSON.stringify(settings));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    },
    
    // تحميل الإعدادات من التخزين المحلي
    loadSettings: function() {
        try {
            const saved = localStorage.getItem('seoToolSettings');
            return saved ? JSON.parse(saved) : DEFAULT_SETTINGS;
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            return DEFAULT_SETTINGS;
        }
    },
    
    // دمج الإعدادات
    mergeSettings: function(defaultSettings, userSettings) {
        return Object.assign({}, defaultSettings, userSettings);
    },
    
    // التحقق من صحة الإعدادات
    validateSettings: function(settings) {
        // التحقق من القيم المطلوبة
        if (!settings.writingStyle || !settings.dialect) {
            return false;
        }
        
        // التحقق من القيم الرقمية
        if (settings.content.keywordDensity < 0.5 || settings.content.keywordDensity > 5) {
            return false;
        }
        
        return true;
    }
};

// تصدير التكوين للاستخدام العام
window.GEMINI_CONFIG = GEMINI_CONFIG;
window.DEFAULT_SETTINGS = DEFAULT_SETTINGS;
window.SUPPORTED_FILE_TYPES = SUPPORTED_FILE_TYPES;
window.MESSAGES = MESSAGES;
window.PROMPT_TEMPLATES = PROMPT_TEMPLATES;
window.EXPORT_SETTINGS = EXPORT_SETTINGS;
window.APP_STATE = APP_STATE;
window.ConfigHelper = ConfigHelper;
