/**
 * معالج المقالات - المعالجة الرئيسية وتحسين المقالات
 * يدير العملية الكاملة من القراءة إلى التحسين
 */

// ===== فئة معالج المقالات =====
class ArticleProcessor {
    constructor() {
        this.isProcessing = false;
        this.isPaused = false;
        this.currentProgress = 0;
        this.totalArticles = 0;
        this.processedArticles = 0;
        this.results = [];
        this.errors = [];
        this.startTime = null;
        this.pauseTime = null;
        this.estimatedTimeRemaining = 0;
    }

    // ===== بدء معالجة المقالات =====
    async processArticles(files, settings) {
        try {
            this.initializeProcessing(files.length);
            
            // قراءة جميع الملفات أولاً
            const fileContents = await this.readAllFiles(files);
            
            // استخراج المقالات من المحتوى
            const articles = this.extractArticlesFromFiles(fileContents);
            
            // تحديث العدد الإجمالي
            this.totalArticles = articles.length;
            this.updateProgressDisplay();
            
            // معالجة كل مقال
            for (let i = 0; i < articles.length; i++) {
                if (!this.isProcessing) break;
                
                // انتظار إذا كانت العملية متوقفة مؤقتاً
                await this.waitIfPaused();
                
                try {
                    await this.processSingleArticle(articles[i], i, settings);
                } catch (error) {
                    this.handleArticleError(articles[i], error, i);
                }
                
                this.processedArticles++;
                this.updateProgress();
            }
            
            this.completeProcessing();
            
        } catch (error) {
            this.handleProcessingError(error);
        }
    }

    // ===== تهيئة المعالجة =====
    initializeProcessing(fileCount) {
        this.isProcessing = true;
        this.isPaused = false;
        this.currentProgress = 0;
        this.totalArticles = fileCount;
        this.processedArticles = 0;
        this.results = [];
        this.errors = [];
        this.startTime = Date.now();
        this.pauseTime = null;
        
        // تحديث حالة التطبيق
        APP_STATE.isProcessing = true;
        APP_STATE.totalArticles = fileCount;
        APP_STATE.processedArticles = 0;
        APP_STATE.startTime = this.startTime;
        
        // إظهار شريط التقدم
        document.getElementById('progressContainer').style.display = 'block';
        this.updateProgressDisplay();
    }

    // ===== قراءة جميع الملفات =====
    async readAllFiles(files) {
        const fileContents = [];
        
        for (let i = 0; i < files.length; i++) {
            try {
                this.updateCurrentStatus(`جاري قراءة الملف: ${files[i].name}`);
                
                const content = await window.FileManager.readFile(files[i]);
                fileContents.push(content);
                
                // تحديث التقدم
                const progress = ((i + 1) / files.length) * 20; // 20% للقراءة
                this.updateProgressBar(progress, `قراءة الملفات: ${i + 1}/${files.length}`);
                
            } catch (error) {
                console.error(`خطأ في قراءة الملف ${files[i].name}:`, error);
                this.errors.push({
                    filename: files[i].name,
                    error: error.message,
                    stage: 'reading'
                });
            }
        }
        
        return fileContents;
    }

    // ===== استخراج المقالات من الملفات =====
    extractArticlesFromFiles(fileContents) {
        const articles = [];
        
        fileContents.forEach((fileContent, fileIndex) => {
            try {
                switch (fileContent.type) {
                    case 'text':
                    case 'html':
                        articles.push({
                            id: `article_${fileIndex}_0`,
                            title: this.extractTitleFromContent(fileContent.content || fileContent.textContent),
                            content: fileContent.content || fileContent.textContent,
                            originalContent: fileContent.content || fileContent.textContent,
                            filename: fileContent.filename,
                            type: fileContent.type,
                            wordCount: fileContent.wordCount,
                            charCount: fileContent.charCount
                        });
                        break;
                        
                    case 'excel':
                    case 'csv':
                        fileContent.articles.forEach((article, articleIndex) => {
                            articles.push({
                                id: `article_${fileIndex}_${articleIndex}`,
                                title: article.title,
                                content: article.content,
                                originalContent: article.content,
                                filename: fileContent.filename,
                                type: fileContent.type,
                                sheet: article.sheet,
                                row: article.row,
                                wordCount: TextHelper.countWords(article.content),
                                charCount: TextHelper.countCharacters(article.content)
                            });
                        });
                        break;
                }
            } catch (error) {
                console.error('خطأ في استخراج المقالات:', error);
                this.errors.push({
                    filename: fileContent.filename,
                    error: error.message,
                    stage: 'extraction'
                });
            }
        });
        
        return articles;
    }

    // ===== استخراج العنوان من المحتوى =====
    extractTitleFromContent(content) {
        // البحث عن عنوان في بداية النص
        const lines = content.split('\n');
        const firstLine = lines[0].trim();
        
        // إذا كان السطر الأول قصير ولا يحتوي على نقطة، فهو على الأرجح عنوان
        if (firstLine.length < 100 && !firstLine.includes('.')) {
            return firstLine;
        }
        
        // البحث عن عناوين HTML
        const h1Match = content.match(/<h1[^>]*>(.*?)<\/h1>/i);
        if (h1Match) {
            return TextHelper.extractTextFromHTML(h1Match[1]);
        }
        
        const h2Match = content.match(/<h2[^>]*>(.*?)<\/h2>/i);
        if (h2Match) {
            return TextHelper.extractTextFromHTML(h2Match[1]);
        }
        
        // إنشاء عنوان من أول 50 حرف
        return content.substring(0, 50).trim() + '...';
    }

    // ===== معالجة مقال واحد =====
    async processSingleArticle(article, index, settings) {
        try {
            this.updateCurrentStatus(`جاري معالجة: ${article.title}`);
            
            // المرحلة 1: استخراج الكلمات المفتاحية
            let keywords = null;
            try {
                this.updateCurrentStatus(`استخراج الكلمات المفتاحية: ${article.title}`);
                const keywordResult = await window.AIEngine.extractKeywords(article.content);
                if (keywordResult.success) {
                    keywords = keywordResult.keywords;
                }
            } catch (error) {
                console.warn('فشل في استخراج الكلمات المفتاحية:', error);
            }
            
            // المرحلة 2: تحسين المقال
            this.updateCurrentStatus(`تحسين المقال: ${article.title}`);
            const optimizationResult = await window.AIEngine.optimizeArticle(article.content, settings);
            
            if (!optimizationResult.success) {
                throw new Error(optimizationResult.error);
            }
            
            // المرحلة 3: معالجة النتيجة
            const processedArticle = this.processOptimizationResult(
                article, 
                optimizationResult, 
                keywords, 
                settings
            );
            
            // إضافة النتيجة
            this.results.push(processedArticle);
            
            // تحديث واجهة النتائج
            this.updateResultsDisplay();
            
        } catch (error) {
            throw new Error(`فشل في معالجة المقال "${article.title}": ${error.message}`);
        }
    }

    // ===== معالجة نتيجة التحسين =====
    processOptimizationResult(originalArticle, optimizationResult, keywords, settings) {
        let optimizedContent = optimizationResult.content;

        // تنظيف المحتوى من النصوص غير المرغوب فيها
        optimizedContent = this.cleanOptimizedContent(optimizedContent);
        
        // تحليل المحتوى المحسن
        const analysis = this.analyzeOptimizedContent(
            originalArticle.content, 
            optimizedContent, 
            keywords
        );
        
        // إنشاء تقرير التحسين
        const improvementReport = this.generateImprovementReport(
            originalArticle, 
            optimizedContent, 
            analysis, 
            settings
        );
        
        return {
            id: originalArticle.id,
            originalTitle: originalArticle.title,
            optimizedTitle: this.extractOptimizedTitle(optimizedContent),
            originalContent: originalArticle.originalContent,
            optimizedContent: optimizedContent,
            filename: originalArticle.filename,
            type: originalArticle.type,
            keywords: keywords,
            analysis: analysis,
            improvementReport: improvementReport,
            usage: optimizationResult.usage,
            processedAt: new Date().toISOString(),
            settings: settings
        };
    }

    // ===== استخراج العنوان المحسن =====
    extractOptimizedTitle(optimizedContent) {
        // البحث عن عنوان H1 أو H2
        const h1Match = optimizedContent.match(/<h1[^>]*>(.*?)<\/h1>/i);
        if (h1Match) {
            return TextHelper.extractTextFromHTML(h1Match[1]);
        }
        
        const h2Match = optimizedContent.match(/<h2[^>]*>(.*?)<\/h2>/i);
        if (h2Match) {
            return TextHelper.extractTextFromHTML(h2Match[1]);
        }
        
        // البحث في بداية النص
        const textContent = TextHelper.extractTextFromHTML(optimizedContent);
        const lines = textContent.split('\n');
        return lines[0].trim() || 'عنوان محسن';
    }

    // ===== تحليل المحتوى المحسن =====
    analyzeOptimizedContent(originalContent, optimizedContent, keywords) {
        const originalText = TextHelper.extractTextFromHTML(originalContent);
        const optimizedText = TextHelper.extractTextFromHTML(optimizedContent);

        // تحليل مفصل للمحتوى المحسن
        const detailedAnalysis = this.performDetailedAnalysis(originalContent, optimizedContent, keywords);

        return {
            originalStats: {
                wordCount: TextHelper.countWords(originalText),
                charCount: TextHelper.countCharacters(originalText),
                paragraphs: originalText.split('\n\n').length,
                headings: this.countHeadings(originalContent)
            },
            optimizedStats: {
                wordCount: TextHelper.countWords(optimizedText),
                charCount: TextHelper.countCharacters(optimizedText),
                paragraphs: optimizedText.split('\n\n').length,
                headings: this.countHeadings(optimizedContent),
                lists: this.countLists(optimizedContent),
                tables: this.countTables(optimizedContent)
            },
            improvements: {
                wordIncrease: TextHelper.countWords(optimizedText) - TextHelper.countWords(originalText),
                structureAdded: this.hasImprovedStructure(originalContent, optimizedContent),
                seoOptimized: this.hasSEOOptimizations(optimizedContent),
                keywordDensity: keywords ? this.calculateKeywordDensity(optimizedText, keywords) : null
            },
            detailedAnalysis: detailedAnalysis
        };
    }

    // ===== التحليل المفصل للمحتوى =====
    performDetailedAnalysis(originalContent, optimizedContent, keywords) {
        const analysis = {
            addedElements: [],
            keywordAnalysis: null,
            seoImprovements: [],
            eatImprovements: [],
            technicalImprovements: []
        };

        // تحليل العناصر المضافة
        const originalHeadings = this.countHeadings(originalContent);
        const optimizedHeadings = this.countHeadings(optimizedContent);

        // تحليل العناوين المضافة
        if (optimizedHeadings.total > originalHeadings.total) {
            const addedHeadings = optimizedHeadings.total - originalHeadings.total;
            analysis.addedElements.push(`تم إضافة ${addedHeadings} عنوان جديد`);

            // تفصيل أنواع العناوين
            const headingDetails = [];
            if (optimizedHeadings.h1 > originalHeadings.h1) {
                headingDetails.push(`${optimizedHeadings.h1 - originalHeadings.h1} عنوان H1`);
            }
            if (optimizedHeadings.h2 > originalHeadings.h2) {
                headingDetails.push(`${optimizedHeadings.h2 - originalHeadings.h2} عنوان H2`);
            }
            if (optimizedHeadings.h3 > originalHeadings.h3) {
                headingDetails.push(`${optimizedHeadings.h3 - originalHeadings.h3} عنوان H3`);
            }
            if (optimizedHeadings.h4 > originalHeadings.h4) {
                headingDetails.push(`${optimizedHeadings.h4 - originalHeadings.h4} عنوان H4`);
            }
            if (optimizedHeadings.h5 > originalHeadings.h5) {
                headingDetails.push(`${optimizedHeadings.h5 - originalHeadings.h5} عنوان H5`);
            }
            if (optimizedHeadings.h6 > originalHeadings.h6) {
                headingDetails.push(`${optimizedHeadings.h6 - originalHeadings.h6} عنوان H6`);
            }

            if (headingDetails.length > 0) {
                analysis.addedElements.push(`تفصيل العناوين: ${headingDetails.join(', ')}`);
            }
        }

        // تحليل القوائم والجداول
        const lists = this.countLists(optimizedContent);
        const tables = this.countTables(optimizedContent);

        if (lists.total > 0) {
            analysis.addedElements.push(`تم إضافة ${lists.total} قائمة (${lists.unordered} نقطية، ${lists.ordered} مرقمة)`);
        }

        if (tables > 0) {
            analysis.addedElements.push(`تم إضافة ${tables} جدول لتنظيم البيانات`);
        }

        // تحليل الكلمات المفتاحية الفعلي
        if (keywords) {
            analysis.keywordAnalysis = this.analyzeKeywordUsage(optimizedContent, keywords);
        }

        // تحليل تحسينات SEO الفعلية
        analysis.seoImprovements = this.identifyActualSEOImprovements(originalContent, optimizedContent);

        // تحليل تحسينات E-A-T الفعلية
        analysis.eatImprovements = this.identifyEATImprovements(optimizedContent);

        // تحليل التحسينات التقنية
        analysis.technicalImprovements = this.identifyTechnicalImprovements(optimizedContent);

        return analysis;
    }

    // ===== عد العناوين =====
    countHeadings(content) {
        const headings = content.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi) || [];
        return {
            total: headings.length,
            h1: (content.match(/<h1[^>]*>.*?<\/h1>/gi) || []).length,
            h2: (content.match(/<h2[^>]*>.*?<\/h2>/gi) || []).length,
            h3: (content.match(/<h3[^>]*>.*?<\/h3>/gi) || []).length,
            h4: (content.match(/<h4[^>]*>.*?<\/h4>/gi) || []).length,
            h5: (content.match(/<h5[^>]*>.*?<\/h5>/gi) || []).length,
            h6: (content.match(/<h6[^>]*>.*?<\/h6>/gi) || []).length
        };
    }

    // ===== عد القوائم =====
    countLists(content) {
        return {
            unordered: (content.match(/<ul[^>]*>.*?<\/ul>/gi) || []).length,
            ordered: (content.match(/<ol[^>]*>.*?<\/ol>/gi) || []).length,
            total: (content.match(/<[uo]l[^>]*>.*?<\/[uo]l>/gi) || []).length
        };
    }

    // ===== عد الجداول =====
    countTables(content) {
        return (content.match(/<table[^>]*>.*?<\/table>/gi) || []).length;
    }

    // ===== التحقق من تحسين البنية =====
    hasImprovedStructure(original, optimized) {
        const originalHeadings = this.countHeadings(original).total;
        const optimizedHeadings = this.countHeadings(optimized).total;
        
        return optimizedHeadings > originalHeadings;
    }

    // ===== التحقق من تحسينات SEO =====
    hasSEOOptimizations(content) {
        const checks = {
            hasTitle: /<h[1-2][^>]*>.*?<\/h[1-2]>/i.test(content),
            hasStructure: this.countHeadings(content).total > 0,
            hasLists: this.countLists(content).total > 0,
            hasFormatting: /<(strong|b|em|i)[^>]*>.*?<\/(strong|b|em|i)>/i.test(content)
        };
        
        return Object.values(checks).filter(Boolean).length >= 3;
    }

    // ===== تحليل استخدام الكلمات المفتاحية =====
    analyzeKeywordUsage(content, keywords) {
        const text = TextHelper.extractTextFromHTML(content).toLowerCase();
        const totalWords = TextHelper.countWords(text);

        const analysis = {
            primary: null,
            secondary: [],
            density: 0,
            locations: []
        };

        if (keywords.primary) {
            const primaryKeyword = keywords.primary.toLowerCase();
            const occurrences = (text.match(new RegExp(primaryKeyword, 'g')) || []).length;
            analysis.primary = {
                keyword: keywords.primary,
                occurrences: occurrences,
                density: (occurrences / totalWords) * 100
            };
            analysis.density = analysis.primary.density;

            // تحديد مواقع الكلمة المفتاحية
            if (content.toLowerCase().includes(primaryKeyword)) {
                if (content.match(new RegExp(`<h[1-6][^>]*>[^<]*${primaryKeyword}[^<]*</h[1-6]>`, 'i'))) {
                    analysis.locations.push('العناوين');
                }
                if (content.toLowerCase().indexOf(primaryKeyword) < 200) {
                    analysis.locations.push('الفقرة الأولى');
                }
                if (content.toLowerCase().lastIndexOf(primaryKeyword) > content.length - 200) {
                    analysis.locations.push('الخاتمة');
                }
            }
        }

        if (keywords.secondary && keywords.secondary.length > 0) {
            keywords.secondary.forEach(keyword => {
                const keywordLower = keyword.toLowerCase();
                const occurrences = (text.match(new RegExp(keywordLower, 'g')) || []).length;
                if (occurrences > 0) {
                    analysis.secondary.push({
                        keyword: keyword,
                        occurrences: occurrences,
                        density: (occurrences / totalWords) * 100
                    });
                }
            });
        }

        return analysis;
    }

    // ===== تحديد تحسينات SEO الفعلية =====
    identifyActualSEOImprovements(originalContent, optimizedContent) {
        const improvements = [];

        // تحليل العناوين
        const originalHeadings = this.countHeadings(originalContent);
        const optimizedHeadings = this.countHeadings(optimizedContent);

        if (optimizedHeadings.total > originalHeadings.total) {
            improvements.push(`تحسين هيكل العناوين: إضافة ${optimizedHeadings.total - originalHeadings.total} عنوان`);
        }

        // تحليل الروابط الداخلية
        const internalLinks = (optimizedContent.match(/<a[^>]*href[^>]*>/gi) || []).length;
        if (internalLinks > 0) {
            improvements.push(`إضافة ${internalLinks} رابط داخلي`);
        }

        // تحليل النص البديل للصور
        const altTexts = (optimizedContent.match(/<img[^>]*alt=[^>]*>/gi) || []).length;
        if (altTexts > 0) {
            improvements.push(`إضافة نص بديل لـ ${altTexts} صورة`);
        }

        // تحليل القوائم
        const lists = this.countLists(optimizedContent);
        if (lists.total > 0) {
            improvements.push(`تحسين القراءة بـ ${lists.total} قائمة منظمة`);
        }

        // تحليل الجداول
        const tables = this.countTables(optimizedContent);
        if (tables > 0) {
            improvements.push(`تنظيم البيانات في ${tables} جدول`);
        }

        return improvements;
    }

    // ===== تحديد تحسينات E-A-T الفعلية =====
    identifyEATImprovements(content) {
        const improvements = [];
        const text = content.toLowerCase();

        // البحث عن مؤشرات الخبرة
        const experienceIndicators = ['تجربة', 'خبرة', 'سنوات', 'عملت', 'شاهدت', 'لاحظت'];
        const foundExperience = experienceIndicators.some(indicator => text.includes(indicator));
        if (foundExperience) {
            improvements.push('إضافة مؤشرات الخبرة العملية في المحتوى');
        }

        // البحث عن مؤشرات السلطة
        const authorityIndicators = ['دراسة', 'بحث', 'إحصائية', 'وفقاً لـ', 'تشير الدراسات'];
        const foundAuthority = authorityIndicators.some(indicator => text.includes(indicator));
        if (foundAuthority) {
            improvements.push('تعزيز السلطة بالمراجع والدراسات');
        }

        // البحث عن مؤشرات الثقة
        const trustIndicators = ['مصدر موثوق', 'معتمد', 'مؤكد', 'موثق'];
        const foundTrust = trustIndicators.some(indicator => text.includes(indicator));
        if (foundTrust) {
            improvements.push('بناء الثقة بمصادر موثوقة');
        }

        return improvements;
    }

    // ===== تحديد التحسينات التقنية الفعلية =====
    identifyTechnicalImprovements(content) {
        const improvements = [];

        // تحليل CSS المدمج
        if (content.includes('<style>') || content.includes('style=')) {
            improvements.push('تحسين التنسيق بـ CSS مدمج');
        }

        // تحليل HTML5 Semantic Tags
        const semanticTags = ['<article>', '<section>', '<header>', '<footer>', '<nav>', '<aside>'];
        const foundSemantic = semanticTags.some(tag => content.includes(tag));
        if (foundSemantic) {
            improvements.push('استخدام HTML5 Semantic Tags');
        }

        // تحليل التوافق مع الأجهزة المحمولة
        if (content.includes('viewport') || content.includes('responsive')) {
            improvements.push('تحسين التوافق مع الأجهزة المحمولة');
        }

        return improvements;
    }

    // ===== حساب كثافة الكلمات المفتاحية =====
    calculateKeywordDensity(text, keywords) {
        if (!keywords || !keywords.primary) return 0;

        const totalWords = TextHelper.countWords(text);
        const keywordOccurrences = (text.toLowerCase().match(new RegExp(keywords.primary.toLowerCase(), 'g')) || []).length;

        return (keywordOccurrences / totalWords) * 100;
    }

    // ===== إنشاء تقرير التحسين =====
    generateImprovementReport(originalArticle, optimizedContent, analysis, settings) {
        const actualImprovements = [];

        // التحسينات الأساسية المؤكدة
        if (analysis.improvements.wordIncrease > 0) {
            actualImprovements.push(`إثراء المحتوى بـ ${analysis.improvements.wordIncrease} كلمة إضافية`);
        }

        // إضافة التحسينات من التحليل المفصل
        if (analysis.detailedAnalysis) {
            // العناصر المضافة فعلياً
            actualImprovements.push(...analysis.detailedAnalysis.addedElements);

            // تحسينات SEO الفعلية
            actualImprovements.push(...analysis.detailedAnalysis.seoImprovements);

            // تحسينات E-A-T الفعلية
            actualImprovements.push(...analysis.detailedAnalysis.eatImprovements);

            // التحسينات التقنية الفعلية
            actualImprovements.push(...analysis.detailedAnalysis.technicalImprovements);
        }

        // تحليل الكلمات المفتاحية الفعلي
        let keywordAnalysisText = '';
        if (analysis.detailedAnalysis && analysis.detailedAnalysis.keywordAnalysis) {
            const kw = analysis.detailedAnalysis.keywordAnalysis;
            if (kw.primary) {
                keywordAnalysisText = `الكلمة المفتاحية الرئيسية: "${kw.primary.keyword}" (${kw.primary.occurrences} مرة، كثافة ${kw.primary.density.toFixed(1)}%)`;
                if (kw.locations.length > 0) {
                    keywordAnalysisText += ` - موجودة في: ${kw.locations.join(', ')}`;
                }
                actualImprovements.push(keywordAnalysisText);
            }

            if (kw.secondary.length > 0) {
                const secondaryText = kw.secondary.map(s => `"${s.keyword}" (${s.occurrences} مرة)`).join(', ');
                actualImprovements.push(`كلمات مفتاحية ثانوية: ${secondaryText}`);
            }
        }

        // إنشاء ملخص دقيق
        const headingIncrease = analysis.optimizedStats.headings.total - (analysis.originalStats.headings?.total || 0);
        let summary = `تم تحسين المقال بإضافة ${analysis.improvements.wordIncrease} كلمة`;

        if (headingIncrease > 0) {
            summary += ` و ${headingIncrease} عنوان`;
        }

        if (analysis.optimizedStats.lists.total > 0) {
            summary += ` و ${analysis.optimizedStats.lists.total} قائمة`;
        }

        if (analysis.optimizedStats.tables > 0) {
            summary += ` و ${analysis.optimizedStats.tables} جدول`;
        }

        return {
            improvements: actualImprovements.filter(imp => imp && imp.trim() !== ''), // إزالة التحسينات الفارغة
            keywordAnalysis: analysis.detailedAnalysis?.keywordAnalysis || null,
            summary: summary,
            score: this.calculateImprovementScore(analysis),
            recommendations: this.generateAdvancedRecommendations(analysis, settings),
            detailedBreakdown: {
                contentImprovements: analysis.detailedAnalysis?.addedElements || [],
                seoImprovements: analysis.detailedAnalysis?.seoImprovements || [],
                eatImprovements: analysis.detailedAnalysis?.eatImprovements || [],
                technicalImprovements: analysis.detailedAnalysis?.technicalImprovements || []
            }
        };
    }

    // ===== حساب نقاط التحسين =====
    calculateImprovementScore(analysis) {
        let score = 0;
        
        // نقاط للكلمات المضافة
        score += Math.min(analysis.improvements.wordIncrease / 100, 20);
        
        // نقاط للعناوين
        score += Math.min(analysis.optimizedStats.headings.total * 5, 25);
        
        // نقاط للقوائم
        score += Math.min(analysis.optimizedStats.lists.total * 3, 15);
        
        // نقاط للجداول
        score += Math.min(analysis.optimizedStats.tables * 5, 20);
        
        // نقاط لتحسينات SEO
        if (analysis.improvements.seoOptimized) score += 20;
        
        return Math.min(Math.round(score), 100);
    }

    // ===== إنشاء التوصيات المتقدمة =====
    generateAdvancedRecommendations(analysis, settings) {
        const recommendations = [];

        // توصيات SEO
        if (analysis.optimizedStats.headings.total < 3) {
            recommendations.push('إضافة المزيد من العناوين الفرعية (H2, H3) لتحسين البنية');
        }

        if (analysis.optimizedStats.lists.total === 0) {
            recommendations.push('استخدام قوائم نقطية ومرقمة لتحسين تجربة القراءة');
        }

        if (analysis.improvements.keywordDensity && analysis.improvements.keywordDensity < 1) {
            recommendations.push('زيادة كثافة الكلمات المفتاحية إلى 1-2% بشكل طبيعي');
        }

        // توصيات E-E-A-T
        recommendations.push('إضافة مراجع ومصادر موثوقة لتعزيز المصداقية');
        recommendations.push('تضمين أمثلة عملية وتجارب شخصية لإظهار الخبرة');
        recommendations.push('إضافة معلومات عن الكاتب أو المؤسسة لبناء السلطة');

        // توصيات تقنية
        if (analysis.optimizedStats.tables === 0) {
            recommendations.push('استخدام جداول لعرض البيانات المقارنة بوضوح');
        }

        recommendations.push('إضافة روابط داخلية لمقالات ذات صلة');
        recommendations.push('تحسين سرعة التحميل بضغط الصور والملفات');
        recommendations.push('إضافة نص بديل للصور (Alt Text) لتحسين الوصولية');

        // توصيات المحتوى
        if (analysis.originalStats.wordCount < 1000) {
            recommendations.push('زيادة طول المحتوى إلى 1000+ كلمة لتحسين الترتيب');
        }

        recommendations.push('إضافة أسئلة شائعة (FAQ) في نهاية المقال');
        recommendations.push('استخدام الكلمات المفتاحية في العناوين والفقرات الأولى');

        return recommendations.slice(0, 8); // أهم 8 توصيات
    }

    // ===== إنشاء التوصيات (للتوافق مع الكود القديم) =====
    generateRecommendations(analysis, settings) {
        return this.generateAdvancedRecommendations(analysis, settings);
    }

    // ===== انتظار إذا كانت العملية متوقفة =====
    async waitIfPaused() {
        while (this.isPaused && this.isProcessing) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    // ===== تحديث التقدم =====
    updateProgress() {
        const percentage = (this.processedArticles / this.totalArticles) * 100;
        this.currentProgress = percentage;
        
        // تحديث شريط التقدم
        this.updateProgressBar(percentage, `${this.processedArticles}/${this.totalArticles} مقال`);
        
        // تحديث الإحصائيات
        document.getElementById('completedCount').textContent = this.processedArticles;
        document.getElementById('remainingCount').textContent = this.totalArticles - this.processedArticles;
        
        // حساب الوقت المتبقي
        this.calculateEstimatedTime();
    }

    // ===== تحديث شريط التقدم =====
    updateProgressBar(percentage, text) {
        const progressBar = document.getElementById('mainProgressBar');
        const progressText = document.getElementById('progressText');
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }
        
        if (progressText) {
            progressText.textContent = text || Math.round(percentage) + '%';
        }
    }

    // ===== تحديث الحالة الحالية =====
    updateCurrentStatus(status) {
        const statusElement = document.getElementById('currentStatus');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    // ===== تحديث عرض التقدم =====
    updateProgressDisplay() {
        document.getElementById('completedCount').textContent = this.processedArticles;
        document.getElementById('remainingCount').textContent = this.totalArticles - this.processedArticles;
    }

    // ===== حساب الوقت المتبقي =====
    calculateEstimatedTime() {
        if (this.processedArticles === 0) return;
        
        const elapsedTime = Date.now() - this.startTime;
        const averageTimePerArticle = elapsedTime / this.processedArticles;
        const remainingArticles = this.totalArticles - this.processedArticles;
        const estimatedTimeRemaining = remainingArticles * averageTimePerArticle;
        
        this.estimatedTimeRemaining = estimatedTimeRemaining;
        
        const estimatedTimeElement = document.getElementById('estimatedTime');
        if (estimatedTimeElement) {
            estimatedTimeElement.textContent = Formatter.formatTime(estimatedTimeRemaining / 1000);
        }
    }

    // ===== تحديث عرض النتائج =====
    updateResultsDisplay() {
        // سيتم تنفيذها في ملف النتائج
        if (window.ResultsManager) {
            window.ResultsManager.updateDisplay(this.results);
        }
    }

    // ===== معالجة خطأ المقال =====
    handleArticleError(article, error, index) {
        console.error(`خطأ في معالجة المقال ${index + 1}:`, error);
        
        this.errors.push({
            articleId: article.id,
            title: article.title,
            filename: article.filename,
            error: error.message,
            stage: 'processing',
            index: index
        });
        
        UIHelper.showAlert(`خطأ في معالجة المقال: ${article.title}`, 'error');
    }

    // ===== معالجة خطأ المعالجة العامة =====
    handleProcessingError(error) {
        console.error('خطأ في المعالجة العامة:', error);
        
        this.isProcessing = false;
        this.updateCurrentStatus('توقفت المعالجة بسبب خطأ');
        
        UIHelper.showAlert(`خطأ في المعالجة: ${error.message}`, 'error');
    }

    // ===== إكمال المعالجة =====
    completeProcessing() {
        this.isProcessing = false;
        this.updateCurrentStatus('تم الانتهاء من المعالجة');
        this.updateProgressBar(100, 'مكتمل');
        
        // إظهار ملخص النتائج
        const successCount = this.results.length;
        const errorCount = this.errors.length;
        
        UIHelper.showAlert(
            `تم الانتهاء من المعالجة! نجح: ${successCount}، فشل: ${errorCount}`, 
            successCount > 0 ? 'success' : 'warning'
        );
        
        // التبديل إلى تبويب النتائج
        if (successCount > 0) {
            document.getElementById('results-tab').click();
        }
    }

    // ===== إيقاف المعالجة =====
    stopProcessing() {
        this.isProcessing = false;
        this.isPaused = false;
        this.updateCurrentStatus('تم إيقاف المعالجة');
    }

    // ===== إيقاف مؤقت =====
    pauseProcessing() {
        this.isPaused = !this.isPaused;
        
        if (this.isPaused) {
            this.pauseTime = Date.now();
            this.updateCurrentStatus('متوقف مؤقتاً');
        } else {
            if (this.pauseTime) {
                this.startTime += Date.now() - this.pauseTime;
            }
            this.updateCurrentStatus('جاري المعالجة');
        }
    }

    // ===== تنظيف المحتوى المحسن =====
    cleanOptimizedContent(content) {
        // إزالة النصوص التمهيدية غير المرغوب فيها
        const unwantedPhrases = [
            /بالتأكيد[!؟]?\s*إليك\s+المقال\s+المُحسَّن/gi,
            /إليك\s+المقال\s+المحسن/gi,
            /بتنسيق\s+HTML\s+مع\s+CSS\s+مدمج/gi,
            /مع\s+مراعاة\s+جميع\s+المتطلبات/gi,
            /```html/gi,
            /```/gi,
            /هنا\s+المقال\s+المحسن/gi,
            /تم\s+تحسين\s+المقال/gi,
            /المقال\s+المحسن\s+كالتالي/gi
        ];

        let cleanedContent = content;

        // إزالة العبارات غير المرغوب فيها
        unwantedPhrases.forEach(phrase => {
            cleanedContent = cleanedContent.replace(phrase, '');
        });

        // إزالة الأسطر الفارغة الزائدة
        cleanedContent = cleanedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

        // إزالة المسافات الزائدة في البداية والنهاية
        cleanedContent = cleanedContent.trim();

        // التأكد من أن المحتوى يبدأ بعنصر HTML صحيح
        if (!cleanedContent.startsWith('<')) {
            // البحث عن أول عنصر HTML
            const htmlMatch = cleanedContent.match(/<[^>]+>/);
            if (htmlMatch) {
                const startIndex = cleanedContent.indexOf(htmlMatch[0]);
                cleanedContent = cleanedContent.substring(startIndex);
            }
        }

        return cleanedContent;
    }

    // ===== الحصول على النتائج =====
    getResults() {
        return {
            results: this.results,
            errors: this.errors,
            stats: {
                total: this.totalArticles,
                processed: this.processedArticles,
                successful: this.results.length,
                failed: this.errors.length,
                processingTime: Date.now() - this.startTime
            }
        };
    }
}

// ===== إنشاء مثيل عام =====
window.ArticleProcessor = new ArticleProcessor();

console.log('تم تحميل معالج المقالات بنجاح');
