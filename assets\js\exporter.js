/**
 * مصدر النتائج - تصدير المقالات المحسنة بصيغ مختلفة
 * يدعم HTML, Excel, Text, PDF, ZIP
 */

// ===== فئة مصدر النتائج =====
class ResultsExporter {
    constructor() {
        this.supportedFormats = ['html', 'excel', 'text', 'pdf', 'zip'];
        this.results = [];
    }

    // ===== تعيين النتائج =====
    setResults(results) {
        this.results = results;
    }

    // ===== تصدير بصيغة HTML =====
    async exportToHTML(results = null, options = {}) {
        const data = results || this.results;
        if (!data || data.length === 0) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        const htmlContent = this.generateHTMLContent(data, options);
        const filename = options.filename || `المقالات_المحسنة_${this.getDateString()}.html`;
        
        FileHelper.downloadFile(htmlContent, filename, 'text/html; charset=utf-8');
        return true;
    }

    // ===== إنشاء محتوى HTML =====
    generateHTMLContent(data, options = {}) {
        const includeCSS = options.includeCSS !== false;
        const includeSearch = options.includeSearch !== false;
        
        let html = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المقالات المحسنة للسيو - ${this.getDateString()}</title>
    <meta name="description" content="مجموعة من المقالات المحسنة للسيو باستخدام الذكاء الصناعي">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
`;

        if (includeCSS) {
            html += this.getEmbeddedCSS();
        }

        html += `
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <header class="header-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="main-title">
                            <i class="fas fa-robot text-primary"></i>
                            المقالات المحسنة للسيو
                        </h1>
                        <p class="subtitle">تم تحسينها باستخدام الذكاء الصناعي في ${this.getDateString()}</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="stats-summary">
                            <div class="stat-item">
                                <strong>${data.length}</strong>
                                <small>مقال محسن</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
`;

        if (includeSearch) {
            html += this.generateSearchSection();
        }

        html += `
        <!-- Articles Content -->
        <main class="articles-section">
            <div class="container">
`;

        // إضافة فهرس المقالات
        html += this.generateTableOfContents(data);

        // إضافة المقالات
        data.forEach((article, index) => {
            html += this.generateArticleHTML(article, index);
        });

        html += `
            </div>
        </main>
        
        <!-- Footer -->
        <footer class="footer-section">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <p>&copy; ${new Date().getFullYear()} أداة تحسين المقالات للسيو. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <p>تم التحسين باستخدام الذكاء الصناعي المتقدم</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
`;

        if (includeSearch) {
            html += this.getSearchScript();
        }

        html += `
</body>
</html>`;

        return html;
    }

    // ===== إنشاء قسم البحث =====
    generateSearchSection() {
        return `
        <!-- Search Section -->
        <section class="search-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="search-box">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="ابحث في المقالات...">
                                <button class="btn btn-primary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
`;
    }

    // ===== إنشاء فهرس المحتويات =====
    generateTableOfContents(data) {
        let toc = `
        <!-- Table of Contents -->
        <section class="toc-section">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-list"></i>
                        فهرس المقالات
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
`;

        data.forEach((article, index) => {
            const colClass = data.length > 6 ? 'col-md-4' : 'col-md-6';
            toc += `
                        <div class="${colClass} mb-3">
                            <a href="#article-${index}" class="toc-link">
                                <i class="fas fa-file-alt text-primary me-2"></i>
                                ${article.optimizedTitle || article.originalTitle}
                            </a>
                        </div>
`;
        });

        toc += `
                    </div>
                </div>
            </div>
        </section>
`;

        return toc;
    }

    // ===== إنشاء HTML للمقال =====
    generateArticleHTML(article, index) {
        const improvementScore = article.improvementReport?.score || 0;
        const scoreClass = improvementScore >= 80 ? 'success' : improvementScore >= 60 ? 'warning' : 'danger';
        
        return `
        <!-- Article ${index + 1} -->
        <article class="article-container" id="article-${index}">
            <div class="card article-card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="article-title">
                                ${article.optimizedTitle || article.originalTitle}
                            </h2>
                            <div class="article-meta">
                                <span class="badge bg-primary me-2">
                                    <i class="fas fa-file"></i>
                                    ${article.filename}
                                </span>
                                <span class="badge bg-secondary me-2">
                                    <i class="fas fa-clock"></i>
                                    ${new Date(article.processedAt).toLocaleDateString('ar-SA')}
                                </span>
                                <span class="badge bg-${scoreClass}">
                                    <i class="fas fa-chart-line"></i>
                                    نقاط التحسين: ${improvementScore}%
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="article-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="toggleOriginal(${index})">
                                    <i class="fas fa-eye"></i>
                                    عرض الأصلي
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="copyArticle(${index})">
                                    <i class="fas fa-copy"></i>
                                    نسخ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Optimized Content -->
                    <div class="optimized-content" id="optimized-${index}">
                        ${article.optimizedContent}
                    </div>
                    
                    <!-- Original Content (Hidden by default) -->
                    <div class="original-content" id="original-${index}" style="display: none;">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> المحتوى الأصلي:</h5>
                        </div>
                        <div class="original-text">
                            ${this.formatOriginalContent(article.originalContent)}
                        </div>
                    </div>
                </div>
                
                <!-- Improvement Report -->
                ${this.generateImprovementReportHTML(article.improvementReport, article.analysis)}
            </div>
        </article>
`;
    }

    // ===== تنسيق المحتوى الأصلي =====
    formatOriginalContent(content) {
        // إذا كان المحتوى HTML، عرضه كما هو
        if (content.includes('<') && content.includes('>')) {
            return content;
        }
        
        // تحويل النص العادي إلى HTML
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');
    }

    // ===== إنشاء تقرير التحسين HTML =====
    generateImprovementReportHTML(report, analysis) {
        if (!report || !analysis) return '';
        
        return `
                <div class="card-footer">
                    <div class="improvement-report">
                        <h5 class="mb-3">
                            <i class="fas fa-chart-bar text-success"></i>
                            تقرير التحسين
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الإحصائيات:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الكلمات:</strong> ${analysis.originalStats.wordCount} → ${analysis.optimizedStats.wordCount} 
                                        <span class="text-success">(+${analysis.improvements.wordIncrease})</span>
                                    </li>
                                    <li><strong>العناوين:</strong> ${analysis.optimizedStats.headings.total}</li>
                                    <li><strong>القوائم:</strong> ${analysis.optimizedStats.lists.total}</li>
                                    <li><strong>الجداول:</strong> ${analysis.optimizedStats.tables}</li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>التحسينات المطبقة:</h6>
                                <ul class="list-unstyled">
                                    ${report.improvements.map(improvement => `<li><i class="fas fa-check text-success me-2"></i>${improvement}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                        
                        ${report.recommendations && report.recommendations.length > 0 ? `
                        <div class="mt-3">
                            <h6>توصيات إضافية:</h6>
                            <ul class="list-unstyled">
                                ${report.recommendations.map(rec => `<li><i class="fas fa-lightbulb text-warning me-2"></i>${rec}</li>`).join('')}
                            </ul>
                        </div>
                        ` : ''}
                    </div>
                </div>
`;
    }

    // ===== الحصول على CSS مدمج =====
    getEmbeddedCSS() {
        return `
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .header-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .main-title {
            color: #2563eb;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #6b7280;
            margin: 0;
        }
        
        .stats-summary {
            text-align: center;
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            color: white;
            padding: 15px;
            border-radius: 10px;
        }
        
        .stat-item strong {
            display: block;
            font-size: 1.5rem;
        }
        
        .search-section {
            margin-bottom: 30px;
        }
        
        .search-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .toc-section {
            margin-bottom: 40px;
        }
        
        .toc-link {
            text-decoration: none;
            color: #374151;
            padding: 8px 12px;
            border-radius: 6px;
            display: block;
            transition: all 0.3s ease;
        }
        
        .toc-link:hover {
            background: #e5e7eb;
            color: #2563eb;
            text-decoration: none;
        }
        
        .article-container {
            margin-bottom: 40px;
        }
        
        .article-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .article-card .card-header {
            background: linear-gradient(90deg, #2563eb, #3b82f6);
            color: white;
            border-bottom: none;
        }
        
        .article-title {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .article-meta {
            margin-top: 10px;
        }
        
        .article-actions .btn {
            margin-left: 5px;
        }
        
        .optimized-content {
            line-height: 1.8;
        }
        
        .optimized-content h1,
        .optimized-content h2,
        .optimized-content h3 {
            color: #2563eb;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        
        .optimized-content table {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }
        
        .optimized-content table th,
        .optimized-content table td {
            padding: 12px;
            border: 1px solid #e5e7eb;
            text-align: right;
        }
        
        .optimized-content table th {
            background: #f3f4f6;
            font-weight: 600;
        }
        
        .optimized-content ul,
        .optimized-content ol {
            margin: 15px 0;
            padding-right: 25px;
        }
        
        .optimized-content li {
            margin-bottom: 8px;
        }
        
        .original-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .improvement-report {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        
        .footer-section {
            background: #1f2937;
            color: white;
            padding: 30px 0;
            margin-top: 50px;
        }
        
        @media (max-width: 768px) {
            .article-actions {
                margin-top: 15px;
            }
            
            .article-actions .btn {
                margin-bottom: 5px;
                width: 100%;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
`;
    }

    // ===== الحصول على سكريبت البحث =====
    getSearchScript() {
        return `
    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const articles = document.querySelectorAll('.article-container');
            
            articles.forEach(article => {
                const content = article.textContent.toLowerCase();
                if (content.includes(searchTerm) || searchTerm === '') {
                    article.style.display = 'block';
                } else {
                    article.style.display = 'none';
                }
            });
        });
        
        // Toggle original content
        function toggleOriginal(index) {
            const optimized = document.getElementById('optimized-' + index);
            const original = document.getElementById('original-' + index);
            const btn = event.target.closest('button');
            
            if (original.style.display === 'none') {
                optimized.style.display = 'none';
                original.style.display = 'block';
                btn.innerHTML = '<i class="fas fa-eye-slash"></i> إخفاء الأصلي';
                btn.classList.remove('btn-outline-primary');
                btn.classList.add('btn-primary');
            } else {
                optimized.style.display = 'block';
                original.style.display = 'none';
                btn.innerHTML = '<i class="fas fa-eye"></i> عرض الأصلي';
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-outline-primary');
            }
        }
        
        // Copy article content
        function copyArticle(index) {
            const content = document.getElementById('optimized-' + index);
            const textContent = content.innerText || content.textContent;
            
            navigator.clipboard.writeText(textContent).then(function() {
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
                btn.classList.remove('btn-outline-success');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-success');
                }, 2000);
            });
        }
        
        // Smooth scrolling for TOC links
        document.querySelectorAll('.toc-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
        
        // Add fade-in animation to articles
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        });
        
        document.querySelectorAll('.article-container').forEach(article => {
            observer.observe(article);
        });
    </script>
`;
    }

    // ===== تصدير بصيغة نص =====
    async exportToText(results = null, options = {}) {
        const data = results || this.results;
        if (!data || data.length === 0) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        let textContent = `المقالات المحسنة للسيو\n`;
        textContent += `تاريخ التصدير: ${this.getDateString()}\n`;
        textContent += `عدد المقالات: ${data.length}\n`;
        textContent += '='.repeat(80) + '\n\n';

        data.forEach((article, index) => {
            textContent += `المقال رقم ${index + 1}\n`;
            textContent += `العنوان: ${article.optimizedTitle || article.originalTitle}\n`;
            textContent += `الملف: ${article.filename}\n`;
            textContent += `نقاط التحسين: ${article.improvementReport?.score || 0}%\n`;
            textContent += '-'.repeat(50) + '\n';
            
            // استخراج النص من HTML
            const textOnly = TextHelper.extractTextFromHTML(article.optimizedContent);
            textContent += textOnly + '\n\n';
            
            if (article.improvementReport && article.improvementReport.improvements) {
                textContent += 'التحسينات المطبقة:\n';
                article.improvementReport.improvements.forEach(improvement => {
                    textContent += `- ${improvement}\n`;
                });
                textContent += '\n';
            }
            
            textContent += '='.repeat(80) + '\n\n';
        });

        const filename = options.filename || `المقالات_المحسنة_${this.getDateString()}.txt`;
        FileHelper.downloadFile(textContent, filename, 'text/plain; charset=utf-8');
        return true;
    }

    // ===== تصدير بصيغة Excel =====
    async exportToExcel(results = null, options = {}) {
        const data = results || this.results;
        if (!data || data.length === 0) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        // تحميل مكتبة SheetJS إذا لم تكن محملة
        if (typeof XLSX === 'undefined') {
            await this.loadSheetJS();
        }

        // إنشاء ورقة العمل الرئيسية
        const worksheetData = [
            ['رقم المقال', 'العنوان المحسن', 'العنوان الأصلي', 'اسم الملف', 'عدد الكلمات الأصلي', 'عدد الكلمات المحسن', 'نقاط التحسين', 'تاريخ المعالجة']
        ];

        data.forEach((article, index) => {
            worksheetData.push([
                index + 1,
                article.optimizedTitle || '',
                article.originalTitle || '',
                article.filename || '',
                article.analysis?.originalStats?.wordCount || 0,
                article.analysis?.optimizedStats?.wordCount || 0,
                article.improvementReport?.score || 0,
                new Date(article.processedAt).toLocaleDateString('ar-SA')
            ]);
        });

        const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
        
        // إنشاء ورقة المحتوى المحسن
        const contentData = [['رقم المقال', 'العنوان', 'المحتوى المحسن']];
        data.forEach((article, index) => {
            const textContent = TextHelper.extractTextFromHTML(article.optimizedContent);
            contentData.push([
                index + 1,
                article.optimizedTitle || article.originalTitle,
                textContent
            ]);
        });
        const contentWorksheet = XLSX.utils.aoa_to_sheet(contentData);

        // إنشاء ورقة التقارير
        const reportsData = [['رقم المقال', 'العنوان', 'التحسينات المطبقة', 'التوصيات']];
        data.forEach((article, index) => {
            const improvements = article.improvementReport?.improvements?.join('; ') || '';
            const recommendations = article.improvementReport?.recommendations?.join('; ') || '';
            reportsData.push([
                index + 1,
                article.optimizedTitle || article.originalTitle,
                improvements,
                recommendations
            ]);
        });
        const reportsWorksheet = XLSX.utils.aoa_to_sheet(reportsData);

        // إنشاء كتاب العمل
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'ملخص المقالات');
        XLSX.utils.book_append_sheet(workbook, contentWorksheet, 'المحتوى المحسن');
        XLSX.utils.book_append_sheet(workbook, reportsWorksheet, 'تقارير التحسين');

        // تصدير الملف
        const filename = options.filename || `المقالات_المحسنة_${this.getDateString()}.xlsx`;
        XLSX.writeFile(workbook, filename);
        return true;
    }

    // ===== تحميل مكتبة SheetJS =====
    async loadSheetJS() {
        return new Promise((resolve, reject) => {
            if (typeof XLSX !== 'undefined') {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            script.onload = resolve;
            script.onerror = () => reject(new Error('فشل في تحميل مكتبة Excel'));
            document.head.appendChild(script);
        });
    }

    // ===== تصدير بصيغة PDF =====
    async exportToPDF(results = null, options = {}) {
        // سيتم تنفيذها باستخدام مكتبة jsPDF
        throw new Error('تصدير PDF قيد التطوير');
    }

    // ===== تصدير بصيغة ZIP =====
    async exportToZIP(results = null, options = {}) {
        const data = results || this.results;
        if (!data || data.length === 0) {
            throw new Error('لا توجد نتائج للتصدير');
        }

        // تحميل مكتبة JSZip
        await this.loadJSZip();

        const zip = new JSZip();

        // إنشاء مجلدات منظمة
        const htmlFolder = zip.folder("HTML_Files");
        const textFolder = zip.folder("Text_Files");
        const reportsFolder = zip.folder("Reports");
        const individualFolder = zip.folder("Individual_Articles");

        // ملف HTML شامل
        const htmlContent = this.generateHTMLContent(data, { includeCSS: true, includeSearch: true });
        htmlFolder.file("جميع_المقالات_المحسنة.html", htmlContent);

        // ملف نصي شامل
        let textContent = `المقالات المحسنة للسيو\nتاريخ التصدير: ${this.getDateString()}\nعدد المقالات: ${data.length}\n${'='.repeat(80)}\n\n`;
        data.forEach((article, index) => {
            textContent += `المقال ${index + 1}: ${article.optimizedTitle || article.originalTitle}\n`;
            textContent += `${'='.repeat(50)}\n`;
            textContent += TextHelper.extractTextFromHTML(article.optimizedContent) + '\n\n';
            textContent += `${'='.repeat(80)}\n\n`;
        });
        textFolder.file("جميع_المقالات_نصي.txt", textContent);

        // تقرير شامل
        let reportContent = `تقرير التحسين الشامل\nتاريخ: ${this.getDateString()}\n\n`;
        reportContent += `📊 إحصائيات عامة:\n`;
        reportContent += `- إجمالي المقالات: ${data.length}\n`;
        reportContent += `- متوسط نقاط التحسين: ${this.calculateAverageScore(data)}%\n`;
        reportContent += `- إجمالي الكلمات المضافة: ${this.calculateTotalWordsAdded(data)}\n\n`;

        reportContent += `📈 تفاصيل كل مقال:\n${'='.repeat(50)}\n`;
        data.forEach((article, index) => {
            reportContent += `\n${index + 1}. ${article.optimizedTitle || article.originalTitle}\n`;
            reportContent += `   📁 الملف الأصلي: ${article.filename}\n`;
            reportContent += `   ⭐ نقاط التحسين: ${article.improvementReport?.score || 0}%\n`;
            reportContent += `   📝 الكلمات: ${article.analysis?.originalStats?.wordCount || 0} → ${article.analysis?.optimizedStats?.wordCount || 0}\n`;

            if (article.improvementReport?.improvements) {
                reportContent += `   ✅ التحسينات المطبقة:\n`;
                article.improvementReport.improvements.forEach(imp => {
                    reportContent += `      - ${imp}\n`;
                });
            }

            if (article.improvementReport?.recommendations) {
                reportContent += `   💡 توصيات إضافية:\n`;
                article.improvementReport.recommendations.forEach(rec => {
                    reportContent += `      - ${rec}\n`;
                });
            }
            reportContent += `\n`;
        });
        reportsFolder.file("تقرير_التحسين_الشامل.txt", reportContent);

        // ملفات فردية لكل مقال
        data.forEach((article, index) => {
            const articleFolder = individualFolder.folder(`مقال_${index + 1}_${this.sanitizeFilename(article.optimizedTitle || article.originalTitle)}`);

            // HTML محسن
            articleFolder.file("المقال_المحسن.html", this.generateSingleArticleHTML(article));

            // نص محسن
            const articleText = TextHelper.extractTextFromHTML(article.optimizedContent);
            articleFolder.file("المقال_المحسن.txt", articleText);

            // تقرير فردي
            const individualReport = this.generateIndividualReport(article);
            articleFolder.file("تقرير_التحسين.txt", individualReport);
        });

        // إنشاء ملف README
        const readmeContent = this.generateReadmeContent(data);
        zip.file("README.txt", readmeContent);

        // تصدير الملف المضغوط
        const filename = options.filename || `المقالات_المحسنة_شامل_${this.getDateString()}.zip`;
        const content = await zip.generateAsync({type:"blob"});

        // تحميل الملف
        const url = URL.createObjectURL(content);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        return true;
    }

    // ===== حساب متوسط النقاط =====
    calculateAverageScore(data) {
        const totalScore = data.reduce((sum, article) => {
            return sum + (article.improvementReport?.score || 0);
        }, 0);
        return Math.round(totalScore / data.length);
    }

    // ===== حساب إجمالي الكلمات المضافة =====
    calculateTotalWordsAdded(data) {
        return data.reduce((sum, article) => {
            return sum + (article.analysis?.improvements?.wordIncrease || 0);
        }, 0);
    }

    // ===== تنظيف اسم الملف =====
    sanitizeFilename(filename) {
        return filename.replace(/[^\w\s-]/g, '').replace(/\s+/g, '_').substring(0, 50);
    }

    // ===== إنشاء HTML لمقال واحد =====
    generateSingleArticleHTML(article) {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${article.optimizedTitle || article.originalTitle}</title>
    <style>
        body { font-family: 'Tajawal', sans-serif; line-height: 1.8; margin: 20px; }
        h1, h2, h3 { color: #2563eb; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; border: 1px solid #ddd; text-align: right; }
        th { background: #f3f4f6; }
    </style>
</head>
<body>
    ${article.optimizedContent}
</body>
</html>`;
    }

    // ===== إنشاء تقرير فردي =====
    generateIndividualReport(article) {
        let report = `تقرير تحسين المقال\n`;
        report += `العنوان: ${article.optimizedTitle || article.originalTitle}\n`;
        report += `الملف الأصلي: ${article.filename}\n`;
        report += `تاريخ المعالجة: ${new Date(article.processedAt).toLocaleString('ar-SA')}\n\n`;

        report += `📊 الإحصائيات:\n`;
        report += `- الكلمات الأصلية: ${article.analysis?.originalStats?.wordCount || 0}\n`;
        report += `- الكلمات المحسنة: ${article.analysis?.optimizedStats?.wordCount || 0}\n`;
        report += `- الكلمات المضافة: ${article.analysis?.improvements?.wordIncrease || 0}\n`;
        report += `- العناوين: ${article.analysis?.optimizedStats?.headings?.total || 0}\n`;
        report += `- القوائم: ${article.analysis?.optimizedStats?.lists?.total || 0}\n`;
        report += `- الجداول: ${article.analysis?.optimizedStats?.tables || 0}\n`;
        report += `- نقاط التحسين: ${article.improvementReport?.score || 0}%\n\n`;

        if (article.improvementReport?.improvements) {
            report += `✅ التحسينات المطبقة:\n`;
            article.improvementReport.improvements.forEach(imp => {
                report += `- ${imp}\n`;
            });
            report += `\n`;
        }

        if (article.improvementReport?.recommendations) {
            report += `💡 توصيات للتحسين:\n`;
            article.improvementReport.recommendations.forEach(rec => {
                report += `- ${rec}\n`;
            });
        }

        return report;
    }

    // ===== إنشاء محتوى README =====
    generateReadmeContent(data) {
        return `📋 ملف المقالات المحسنة للسيو
${'='.repeat(50)}

📅 تاريخ التصدير: ${this.getDateString()}
📊 عدد المقالات: ${data.length}
⭐ متوسط نقاط التحسين: ${this.calculateAverageScore(data)}%
📝 إجمالي الكلمات المضافة: ${this.calculateTotalWordsAdded(data)}

📁 محتويات الملف المضغوط:
${'='.repeat(30)}

📂 HTML_Files/
   └── جميع_المقالات_المحسنة.html (ملف HTML شامل مع محرك بحث)

📂 Text_Files/
   └── جميع_المقالات_نصي.txt (جميع المقالات بصيغة نصية)

📂 Reports/
   └── تقرير_التحسين_الشامل.txt (تقرير مفصل لجميع التحسينات)

📂 Individual_Articles/
   ├── مقال_1_[اسم_المقال]/
   │   ├── المقال_المحسن.html
   │   ├── المقال_المحسن.txt
   │   └── تقرير_التحسين.txt
   └── ... (مجلد لكل مقال)

🔧 كيفية الاستخدام:
- افتح ملف HTML الشامل في المتصفح لعرض جميع المقالات
- استخدم الملفات النصية للنسخ واللصق
- راجع التقارير لفهم التحسينات المطبقة
- استخدم الملفات الفردية للعمل على مقال واحد

✨ تم إنشاء هذا الملف بواسطة أداة تحسين المقالات للسيو بالذكاء الصناعي`;
    }

    // ===== تحميل مكتبة JSZip =====
    async loadJSZip() {
        return new Promise((resolve, reject) => {
            if (typeof JSZip !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            script.onload = resolve;
            script.onerror = () => reject(new Error('فشل في تحميل مكتبة JSZip'));
            document.head.appendChild(script);
        });
    }

    // ===== الحصول على تاريخ منسق =====
    getDateString() {
        const now = new Date();
        return now.toISOString().split('T')[0];
    }

    // ===== تصدير جميع الصيغ =====
    async exportAll(results = null, options = {}) {
        const data = results || this.results;
        const baseFilename = options.baseFilename || `المقالات_المحسنة_${this.getDateString()}`;
        
        const exports = [];
        
        try {
            // HTML
            await this.exportToHTML(data, { filename: `${baseFilename}.html` });
            exports.push('HTML');
            
            // Text
            await this.exportToText(data, { filename: `${baseFilename}.txt` });
            exports.push('Text');
            
            // Excel
            await this.exportToExcel(data, { filename: `${baseFilename}.xlsx` });
            exports.push('Excel');
            
            return {
                success: true,
                exported: exports,
                message: `تم تصدير ${exports.length} ملف بنجاح`
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                exported: exports
            };
        }
    }
}

// ===== إنشاء مثيل عام =====
window.ResultsExporter = new ResultsExporter();

console.log('تم تحميل مصدر النتائج بنجاح');
