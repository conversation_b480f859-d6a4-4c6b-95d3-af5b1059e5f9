/**
 * ملف الوظائف المساعدة العامة
 * يحتوي على دوال مساعدة للتعامل مع البيانات والواجهة
 */

// ===== دوال التحقق من صحة البيانات =====
const Validator = {
    // التحقق من صحة البريد الإلكتروني
    isValidEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    // التحقق من صحة الرابط
    isValidURL: function(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },
    
    // التحقق من نوع الملف
    isValidFileType: function(fileName, allowedTypes) {
        const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
        return allowedTypes.includes(extension);
    },
    
    // التحقق من حجم الملف
    isValidFileSize: function(fileSize, maxSize) {
        return fileSize <= maxSize;
    },
    
    // التحقق من النص العربي
    isArabicText: function(text) {
        const arabicRegex = /[\u0600-\u06FF]/;
        return arabicRegex.test(text);
    }
};

// ===== دوال التنسيق =====
const Formatter = {
    // تنسيق حجم الملف
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // تنسيق الوقت
    formatTime: function(seconds) {
        if (seconds < 60) {
            return Math.round(seconds) + ' ثانية';
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.round(seconds % 60);
            return minutes + ' دقيقة' + (remainingSeconds > 0 ? ' و ' + remainingSeconds + ' ثانية' : '');
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return hours + ' ساعة' + (minutes > 0 ? ' و ' + minutes + ' دقيقة' : '');
        }
    },
    
    // تنسيق التاريخ
    formatDate: function(date) {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return new Intl.DateTimeFormat('ar-SA', options).format(date);
    },
    
    // تنسيق الأرقام
    formatNumber: function(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    },
    
    // تنسيق النسبة المئوية
    formatPercentage: function(value, decimals = 1) {
        return (value * 100).toFixed(decimals) + '%';
    }
};

// ===== دوال النصوص =====
const TextHelper = {
    // تنظيف النص
    cleanText: function(text) {
        return text
            .replace(/\s+/g, ' ')           // إزالة المسافات الزائدة
            .replace(/\n\s*\n/g, '\n\n')   // تنظيم الأسطر الفارغة
            .trim();                        // إزالة المسافات من البداية والنهاية
    },
    
    // عد الكلمات
    countWords: function(text) {
        const cleanedText = this.cleanText(text);
        return cleanedText.split(/\s+/).filter(word => word.length > 0).length;
    },
    
    // عد الأحرف
    countCharacters: function(text, includeSpaces = true) {
        return includeSpaces ? text.length : text.replace(/\s/g, '').length;
    },
    
    // استخراج النص من HTML
    extractTextFromHTML: function(html) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        return tempDiv.textContent || tempDiv.innerText || '';
    },
    
    // تقسيم النص إلى أجزاء
    splitText: function(text, maxLength) {
        const words = text.split(' ');
        const chunks = [];
        let currentChunk = '';
        
        for (const word of words) {
            if ((currentChunk + ' ' + word).length <= maxLength) {
                currentChunk += (currentChunk ? ' ' : '') + word;
            } else {
                if (currentChunk) {
                    chunks.push(currentChunk);
                }
                currentChunk = word;
            }
        }
        
        if (currentChunk) {
            chunks.push(currentChunk);
        }
        
        return chunks;
    },
    
    // إنشاء معرف فريد
    generateId: function(prefix = 'id') {
        return prefix + '_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
};

// ===== دوال الواجهة =====
const UIHelper = {
    // إظهار رسالة تنبيه
    showAlert: function(message, type = 'info', duration = 5000) {
        const alertContainer = document.getElementById('alertContainer') || this.createAlertContainer();
        
        const alertElement = document.createElement('div');
        alertElement.className = `alert alert-${type} alert-dismissible fade show`;
        alertElement.innerHTML = `
            <i class="fas fa-${this.getAlertIcon(type)}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertContainer.appendChild(alertElement);
        
        // إزالة التنبيه تلقائياً
        setTimeout(() => {
            if (alertElement.parentNode) {
                alertElement.remove();
            }
        }, duration);
    },
    
    // إنشاء حاوي التنبيهات
    createAlertContainer: function() {
        const container = document.createElement('div');
        container.id = 'alertContainer';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    },
    
    // الحصول على أيقونة التنبيه
    getAlertIcon: function(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // إظهار مؤشر التحميل
    showLoading: function(element, text = 'جاري التحميل...') {
        element.innerHTML = `
            <div class="d-flex align-items-center justify-content-center">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                <span>${text}</span>
            </div>
        `;
        element.disabled = true;
    },
    
    // إخفاء مؤشر التحميل
    hideLoading: function(element, originalText) {
        element.innerHTML = originalText;
        element.disabled = false;
    },
    
    // تحديث شريط التقدم
    updateProgress: function(progressBar, percentage, text = '') {
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
        
        if (text) {
            progressBar.textContent = text;
        } else {
            progressBar.textContent = Math.round(percentage) + '%';
        }
    },
    
    // إنشاء عنصر HTML
    createElement: function(tag, className = '', innerHTML = '') {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    }
};

// ===== دوال التخزين =====
const StorageHelper = {
    // حفظ البيانات في التخزين المحلي
    save: function(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    },
    
    // تحميل البيانات من التخزين المحلي
    load: function(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return defaultValue;
        }
    },
    
    // حذف البيانات من التخزين المحلي
    remove: function(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('خطأ في حذف البيانات:', error);
            return false;
        }
    },
    
    // مسح جميع البيانات
    clear: function() {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    }
};

// ===== دوال الملفات =====
const FileHelper = {
    // قراءة الملف كنص
    readAsText: function(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(e);
            reader.readAsText(file, 'UTF-8');
        });
    },
    
    // تحويل النص إلى ملف
    textToFile: function(text, filename, type = 'text/plain') {
        const blob = new Blob([text], { type });
        return new File([blob], filename, { type });
    },
    
    // تنزيل الملف
    downloadFile: function(content, filename, type = 'text/plain') {
        const blob = new Blob([content], { type });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
    },
    
    // ضغط الملفات
    createZip: function(files) {
        // سيتم تنفيذها باستخدام مكتبة JSZip
        return new Promise((resolve, reject) => {
            // TODO: تنفيذ ضغط الملفات
            resolve(null);
        });
    }
};

// ===== دوال الشبكة =====
const NetworkHelper = {
    // التحقق من الاتصال بالإنترنت
    isOnline: function() {
        return navigator.onLine;
    },
    
    // إرسال طلب HTTP
    request: function(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const finalOptions = Object.assign({}, defaultOptions, options);
        
        return fetch(url, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            });
    },
    
    // معالجة الأخطاء
    handleError: function(error) {
        console.error('خطأ في الشبكة:', error);
        
        if (!this.isOnline()) {
            return 'لا يوجد اتصال بالإنترنت';
        }
        
        if (error.name === 'TypeError') {
            return 'خطأ في الاتصال بالخادم';
        }
        
        return error.message || 'حدث خطأ غير متوقع';
    }
};

// تصدير الدوال للاستخدام العام
window.Validator = Validator;
window.Formatter = Formatter;
window.TextHelper = TextHelper;
window.UIHelper = UIHelper;
window.StorageHelper = StorageHelper;
window.FileHelper = FileHelper;
window.NetworkHelper = NetworkHelper;
