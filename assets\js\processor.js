/**
 * معالج المقالات - المعالجة الرئيسية وتحسين المقالات
 * يدير العملية الكاملة من القراءة إلى التحسين
 */

// ===== فئة معالج المقالات =====
class ArticleProcessor {
    constructor() {
        this.isProcessing = false;
        this.isPaused = false;
        this.currentProgress = 0;
        this.totalArticles = 0;
        this.processedArticles = 0;
        this.results = [];
        this.errors = [];
        this.startTime = null;
        this.pauseTime = null;
        this.estimatedTimeRemaining = 0;
    }

    // ===== بدء معالجة المقالات =====
    async processArticles(files, settings) {
        try {
            this.initializeProcessing(files.length);
            
            // قراءة جميع الملفات أولاً
            const fileContents = await this.readAllFiles(files);
            
            // استخراج المقالات من المحتوى
            const articles = this.extractArticlesFromFiles(fileContents);
            
            // تحديث العدد الإجمالي
            this.totalArticles = articles.length;
            this.updateProgressDisplay();
            
            // معالجة كل مقال
            for (let i = 0; i < articles.length; i++) {
                if (!this.isProcessing) break;
                
                // انتظار إذا كانت العملية متوقفة مؤقتاً
                await this.waitIfPaused();
                
                try {
                    await this.processSingleArticle(articles[i], i, settings);
                } catch (error) {
                    this.handleArticleError(articles[i], error, i);
                }
                
                this.processedArticles++;
                this.updateProgress();
            }
            
            this.completeProcessing();
            
        } catch (error) {
            this.handleProcessingError(error);
        }
    }

    // ===== تهيئة المعالجة =====
    initializeProcessing(fileCount) {
        this.isProcessing = true;
        this.isPaused = false;
        this.currentProgress = 0;
        this.totalArticles = fileCount;
        this.processedArticles = 0;
        this.results = [];
        this.errors = [];
        this.startTime = Date.now();
        this.pauseTime = null;
        
        // تحديث حالة التطبيق
        APP_STATE.isProcessing = true;
        APP_STATE.totalArticles = fileCount;
        APP_STATE.processedArticles = 0;
        APP_STATE.startTime = this.startTime;
        
        // إظهار شريط التقدم
        document.getElementById('progressContainer').style.display = 'block';
        this.updateProgressDisplay();
    }

    // ===== قراءة جميع الملفات =====
    async readAllFiles(files) {
        const fileContents = [];
        
        for (let i = 0; i < files.length; i++) {
            try {
                this.updateCurrentStatus(`جاري قراءة الملف: ${files[i].name}`);
                
                const content = await window.FileManager.readFile(files[i]);
                fileContents.push(content);
                
                // تحديث التقدم
                const progress = ((i + 1) / files.length) * 20; // 20% للقراءة
                this.updateProgressBar(progress, `قراءة الملفات: ${i + 1}/${files.length}`);
                
            } catch (error) {
                console.error(`خطأ في قراءة الملف ${files[i].name}:`, error);
                this.errors.push({
                    filename: files[i].name,
                    error: error.message,
                    stage: 'reading'
                });
            }
        }
        
        return fileContents;
    }

    // ===== استخراج المقالات من الملفات =====
    extractArticlesFromFiles(fileContents) {
        const articles = [];
        
        fileContents.forEach((fileContent, fileIndex) => {
            try {
                switch (fileContent.type) {
                    case 'text':
                    case 'html':
                        articles.push({
                            id: `article_${fileIndex}_0`,
                            title: this.extractTitleFromContent(fileContent.content || fileContent.textContent),
                            content: fileContent.content || fileContent.textContent,
                            originalContent: fileContent.content || fileContent.textContent,
                            filename: fileContent.filename,
                            type: fileContent.type,
                            wordCount: fileContent.wordCount,
                            charCount: fileContent.charCount
                        });
                        break;
                        
                    case 'excel':
                    case 'csv':
                        fileContent.articles.forEach((article, articleIndex) => {
                            articles.push({
                                id: `article_${fileIndex}_${articleIndex}`,
                                title: article.title,
                                content: article.content,
                                originalContent: article.content,
                                filename: fileContent.filename,
                                type: fileContent.type,
                                sheet: article.sheet,
                                row: article.row,
                                wordCount: TextHelper.countWords(article.content),
                                charCount: TextHelper.countCharacters(article.content)
                            });
                        });
                        break;
                }
            } catch (error) {
                console.error('خطأ في استخراج المقالات:', error);
                this.errors.push({
                    filename: fileContent.filename,
                    error: error.message,
                    stage: 'extraction'
                });
            }
        });
        
        return articles;
    }

    // ===== استخراج العنوان من المحتوى =====
    extractTitleFromContent(content) {
        // البحث عن عنوان في بداية النص
        const lines = content.split('\n');
        const firstLine = lines[0].trim();
        
        // إذا كان السطر الأول قصير ولا يحتوي على نقطة، فهو على الأرجح عنوان
        if (firstLine.length < 100 && !firstLine.includes('.')) {
            return firstLine;
        }
        
        // البحث عن عناوين HTML
        const h1Match = content.match(/<h1[^>]*>(.*?)<\/h1>/i);
        if (h1Match) {
            return TextHelper.extractTextFromHTML(h1Match[1]);
        }
        
        const h2Match = content.match(/<h2[^>]*>(.*?)<\/h2>/i);
        if (h2Match) {
            return TextHelper.extractTextFromHTML(h2Match[1]);
        }
        
        // إنشاء عنوان من أول 50 حرف
        return content.substring(0, 50).trim() + '...';
    }

    // ===== معالجة مقال واحد =====
    async processSingleArticle(article, index, settings) {
        try {
            this.updateCurrentStatus(`جاري معالجة: ${article.title}`);
            
            // المرحلة 1: استخراج الكلمات المفتاحية
            let keywords = null;
            try {
                this.updateCurrentStatus(`استخراج الكلمات المفتاحية: ${article.title}`);
                const keywordResult = await window.AIEngine.extractKeywords(article.content);
                if (keywordResult.success) {
                    keywords = keywordResult.keywords;
                }
            } catch (error) {
                console.warn('فشل في استخراج الكلمات المفتاحية:', error);
            }
            
            // المرحلة 2: تحسين المقال
            this.updateCurrentStatus(`تحسين المقال: ${article.title}`);
            const optimizationResult = await window.AIEngine.optimizeArticle(article.content, settings);
            
            if (!optimizationResult.success) {
                throw new Error(optimizationResult.error);
            }
            
            // المرحلة 3: معالجة النتيجة
            const processedArticle = this.processOptimizationResult(
                article, 
                optimizationResult, 
                keywords, 
                settings
            );
            
            // إضافة النتيجة
            this.results.push(processedArticle);
            
            // تحديث واجهة النتائج
            this.updateResultsDisplay();
            
        } catch (error) {
            throw new Error(`فشل في معالجة المقال "${article.title}": ${error.message}`);
        }
    }

    // ===== معالجة نتيجة التحسين =====
    processOptimizationResult(originalArticle, optimizationResult, keywords, settings) {
        let optimizedContent = optimizationResult.content;

        // تنظيف المحتوى من النصوص غير المرغوب فيها
        optimizedContent = this.cleanOptimizedContent(optimizedContent);
        
        // تحليل المحتوى المحسن
        const analysis = this.analyzeOptimizedContent(
            originalArticle.content, 
            optimizedContent, 
            keywords
        );
        
        // إنشاء تقرير التحسين
        const improvementReport = this.generateImprovementReport(
            originalArticle, 
            optimizedContent, 
            analysis, 
            settings
        );
        
        return {
            id: originalArticle.id,
            originalTitle: originalArticle.title,
            optimizedTitle: this.extractOptimizedTitle(optimizedContent),
            originalContent: originalArticle.originalContent,
            optimizedContent: optimizedContent,
            filename: originalArticle.filename,
            type: originalArticle.type,
            keywords: keywords,
            analysis: analysis,
            improvementReport: improvementReport,
            usage: optimizationResult.usage,
            processedAt: new Date().toISOString(),
            settings: settings
        };
    }

    // ===== استخراج العنوان المحسن =====
    extractOptimizedTitle(optimizedContent) {
        // البحث عن عنوان H1 أو H2
        const h1Match = optimizedContent.match(/<h1[^>]*>(.*?)<\/h1>/i);
        if (h1Match) {
            return TextHelper.extractTextFromHTML(h1Match[1]);
        }
        
        const h2Match = optimizedContent.match(/<h2[^>]*>(.*?)<\/h2>/i);
        if (h2Match) {
            return TextHelper.extractTextFromHTML(h2Match[1]);
        }
        
        // البحث في بداية النص
        const textContent = TextHelper.extractTextFromHTML(optimizedContent);
        const lines = textContent.split('\n');
        return lines[0].trim() || 'عنوان محسن';
    }

    // ===== تحليل المحتوى المحسن =====
    analyzeOptimizedContent(originalContent, optimizedContent, keywords) {
        const originalText = TextHelper.extractTextFromHTML(originalContent);
        const optimizedText = TextHelper.extractTextFromHTML(optimizedContent);
        
        return {
            originalStats: {
                wordCount: TextHelper.countWords(originalText),
                charCount: TextHelper.countCharacters(originalText),
                paragraphs: originalText.split('\n\n').length
            },
            optimizedStats: {
                wordCount: TextHelper.countWords(optimizedText),
                charCount: TextHelper.countCharacters(optimizedText),
                paragraphs: optimizedText.split('\n\n').length,
                headings: this.countHeadings(optimizedContent),
                lists: this.countLists(optimizedContent),
                tables: this.countTables(optimizedContent)
            },
            improvements: {
                wordIncrease: TextHelper.countWords(optimizedText) - TextHelper.countWords(originalText),
                structureAdded: this.hasImprovedStructure(originalContent, optimizedContent),
                seoOptimized: this.hasSEOOptimizations(optimizedContent),
                keywordDensity: keywords ? this.calculateKeywordDensity(optimizedText, keywords) : null
            }
        };
    }

    // ===== عد العناوين =====
    countHeadings(content) {
        const headings = content.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi) || [];
        return {
            total: headings.length,
            h1: (content.match(/<h1[^>]*>.*?<\/h1>/gi) || []).length,
            h2: (content.match(/<h2[^>]*>.*?<\/h2>/gi) || []).length,
            h3: (content.match(/<h3[^>]*>.*?<\/h3>/gi) || []).length
        };
    }

    // ===== عد القوائم =====
    countLists(content) {
        return {
            unordered: (content.match(/<ul[^>]*>.*?<\/ul>/gi) || []).length,
            ordered: (content.match(/<ol[^>]*>.*?<\/ol>/gi) || []).length,
            total: (content.match(/<[uo]l[^>]*>.*?<\/[uo]l>/gi) || []).length
        };
    }

    // ===== عد الجداول =====
    countTables(content) {
        return (content.match(/<table[^>]*>.*?<\/table>/gi) || []).length;
    }

    // ===== التحقق من تحسين البنية =====
    hasImprovedStructure(original, optimized) {
        const originalHeadings = this.countHeadings(original).total;
        const optimizedHeadings = this.countHeadings(optimized).total;
        
        return optimizedHeadings > originalHeadings;
    }

    // ===== التحقق من تحسينات SEO =====
    hasSEOOptimizations(content) {
        const checks = {
            hasTitle: /<h[1-2][^>]*>.*?<\/h[1-2]>/i.test(content),
            hasStructure: this.countHeadings(content).total > 0,
            hasLists: this.countLists(content).total > 0,
            hasFormatting: /<(strong|b|em|i)[^>]*>.*?<\/(strong|b|em|i)>/i.test(content)
        };
        
        return Object.values(checks).filter(Boolean).length >= 3;
    }

    // ===== حساب كثافة الكلمات المفتاحية =====
    calculateKeywordDensity(text, keywords) {
        if (!keywords || !keywords.primary) return 0;
        
        const totalWords = TextHelper.countWords(text);
        const keywordOccurrences = (text.toLowerCase().match(new RegExp(keywords.primary.toLowerCase(), 'g')) || []).length;
        
        return (keywordOccurrences / totalWords) * 100;
    }

    // ===== إنشاء تقرير التحسين =====
    generateImprovementReport(originalArticle, optimizedContent, analysis, settings) {
        const improvements = [];
        const seoImprovements = [];
        const eatImprovements = [];

        // تحسينات المحتوى الأساسية
        if (analysis.improvements.wordIncrease > 0) {
            improvements.push(`إثراء المحتوى بـ ${analysis.improvements.wordIncrease} كلمة إضافية`);
        }

        // تحسينات البنية والتنظيم
        if (analysis.optimizedStats.headings.total > 0) {
            improvements.push(`تحسين البنية بـ ${analysis.optimizedStats.headings.total} عنوان محسن للسيو`);
            seoImprovements.push(`هيكلة العناوين (H1-H6) لتحسين فهم المحتوى`);
        }

        if (analysis.optimizedStats.lists.total > 0) {
            improvements.push(`إضافة ${analysis.optimizedStats.lists.total} قائمة منظمة لتحسين القراءة`);
            seoImprovements.push(`قوائم منظمة لتحسين تجربة المستخدم`);
        }

        if (analysis.optimizedStats.tables > 0) {
            improvements.push(`تنظيم البيانات في ${analysis.optimizedStats.tables} جدول احترافي`);
            seoImprovements.push(`جداول منظمة لعرض البيانات بوضوح`);
        }

        // تحسينات SEO متقدمة
        seoImprovements.push(`تحسين كثافة الكلمات المفتاحية إلى ${settings.content.keywordDensity}%`);
        seoImprovements.push(`استهداف كلمات مفتاحية طويلة الذيل (Long-tail keywords)`);
        seoImprovements.push(`تحسين العنوان الرئيسي للسيو (Title Tag optimization)`);
        seoImprovements.push(`إضافة وصف تعريفي محسن (Meta Description)`);
        seoImprovements.push(`استخدام الكلمات المفتاحية LSI (Latent Semantic Indexing)`);

        // تحسينات E-E-A-T
        eatImprovements.push(`إظهار الخبرة العملية (Experience) في الموضوع`);
        eatImprovements.push(`إثبات السلطة (Authority) بمعلومات دقيقة ومتخصصة`);
        eatImprovements.push(`بناء الثقة (Trust) بمحتوى موثوق ومراجع صحيحة`);
        eatImprovements.push(`إضافة الخبرة المهنية (Expertise) والمصداقية`);

        // تحسينات تقنية
        const technicalImprovements = [];
        technicalImprovements.push(`تحسين سرعة التحميل بـ CSS مُحسن`);
        technicalImprovements.push(`تحسين التوافق مع الأجهزة المحمولة (Mobile-First)`);
        technicalImprovements.push(`تحسين إمكانية الوصول (Accessibility)`);
        technicalImprovements.push(`استخدام HTML5 Semantic Tags`);

        // دمج جميع التحسينات
        const allImprovements = [
            ...improvements,
            ...seoImprovements.slice(0, 3), // أهم 3 تحسينات SEO
            ...eatImprovements.slice(0, 2), // أهم 2 تحسينات E-E-A-T
            ...technicalImprovements.slice(0, 2) // أهم 2 تحسينات تقنية
        ];

        return {
            improvements: allImprovements,
            seoImprovements: seoImprovements,
            eatImprovements: eatImprovements,
            technicalImprovements: technicalImprovements,
            summary: `تم تحسين المقال وفقاً لأحدث معايير SEO و E-E-A-T مع إضافة ${analysis.improvements.wordIncrease} كلمة و ${analysis.optimizedStats.headings.total} عنوان محسن`,
            score: this.calculateImprovementScore(analysis),
            recommendations: this.generateAdvancedRecommendations(analysis, settings)
        };
    }

    // ===== حساب نقاط التحسين =====
    calculateImprovementScore(analysis) {
        let score = 0;
        
        // نقاط للكلمات المضافة
        score += Math.min(analysis.improvements.wordIncrease / 100, 20);
        
        // نقاط للعناوين
        score += Math.min(analysis.optimizedStats.headings.total * 5, 25);
        
        // نقاط للقوائم
        score += Math.min(analysis.optimizedStats.lists.total * 3, 15);
        
        // نقاط للجداول
        score += Math.min(analysis.optimizedStats.tables * 5, 20);
        
        // نقاط لتحسينات SEO
        if (analysis.improvements.seoOptimized) score += 20;
        
        return Math.min(Math.round(score), 100);
    }

    // ===== إنشاء التوصيات المتقدمة =====
    generateAdvancedRecommendations(analysis, settings) {
        const recommendations = [];

        // توصيات SEO
        if (analysis.optimizedStats.headings.total < 3) {
            recommendations.push('إضافة المزيد من العناوين الفرعية (H2, H3) لتحسين البنية');
        }

        if (analysis.optimizedStats.lists.total === 0) {
            recommendations.push('استخدام قوائم نقطية ومرقمة لتحسين تجربة القراءة');
        }

        if (analysis.improvements.keywordDensity && analysis.improvements.keywordDensity < 1) {
            recommendations.push('زيادة كثافة الكلمات المفتاحية إلى 1-2% بشكل طبيعي');
        }

        // توصيات E-E-A-T
        recommendations.push('إضافة مراجع ومصادر موثوقة لتعزيز المصداقية');
        recommendations.push('تضمين أمثلة عملية وتجارب شخصية لإظهار الخبرة');
        recommendations.push('إضافة معلومات عن الكاتب أو المؤسسة لبناء السلطة');

        // توصيات تقنية
        if (analysis.optimizedStats.tables === 0) {
            recommendations.push('استخدام جداول لعرض البيانات المقارنة بوضوح');
        }

        recommendations.push('إضافة روابط داخلية لمقالات ذات صلة');
        recommendations.push('تحسين سرعة التحميل بضغط الصور والملفات');
        recommendations.push('إضافة نص بديل للصور (Alt Text) لتحسين الوصولية');

        // توصيات المحتوى
        if (analysis.originalStats.wordCount < 1000) {
            recommendations.push('زيادة طول المحتوى إلى 1000+ كلمة لتحسين الترتيب');
        }

        recommendations.push('إضافة أسئلة شائعة (FAQ) في نهاية المقال');
        recommendations.push('استخدام الكلمات المفتاحية في العناوين والفقرات الأولى');

        return recommendations.slice(0, 8); // أهم 8 توصيات
    }

    // ===== إنشاء التوصيات (للتوافق مع الكود القديم) =====
    generateRecommendations(analysis, settings) {
        return this.generateAdvancedRecommendations(analysis, settings);
    }

    // ===== انتظار إذا كانت العملية متوقفة =====
    async waitIfPaused() {
        while (this.isPaused && this.isProcessing) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    // ===== تحديث التقدم =====
    updateProgress() {
        const percentage = (this.processedArticles / this.totalArticles) * 100;
        this.currentProgress = percentage;
        
        // تحديث شريط التقدم
        this.updateProgressBar(percentage, `${this.processedArticles}/${this.totalArticles} مقال`);
        
        // تحديث الإحصائيات
        document.getElementById('completedCount').textContent = this.processedArticles;
        document.getElementById('remainingCount').textContent = this.totalArticles - this.processedArticles;
        
        // حساب الوقت المتبقي
        this.calculateEstimatedTime();
    }

    // ===== تحديث شريط التقدم =====
    updateProgressBar(percentage, text) {
        const progressBar = document.getElementById('mainProgressBar');
        const progressText = document.getElementById('progressText');
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }
        
        if (progressText) {
            progressText.textContent = text || Math.round(percentage) + '%';
        }
    }

    // ===== تحديث الحالة الحالية =====
    updateCurrentStatus(status) {
        const statusElement = document.getElementById('currentStatus');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    // ===== تحديث عرض التقدم =====
    updateProgressDisplay() {
        document.getElementById('completedCount').textContent = this.processedArticles;
        document.getElementById('remainingCount').textContent = this.totalArticles - this.processedArticles;
    }

    // ===== حساب الوقت المتبقي =====
    calculateEstimatedTime() {
        if (this.processedArticles === 0) return;
        
        const elapsedTime = Date.now() - this.startTime;
        const averageTimePerArticle = elapsedTime / this.processedArticles;
        const remainingArticles = this.totalArticles - this.processedArticles;
        const estimatedTimeRemaining = remainingArticles * averageTimePerArticle;
        
        this.estimatedTimeRemaining = estimatedTimeRemaining;
        
        const estimatedTimeElement = document.getElementById('estimatedTime');
        if (estimatedTimeElement) {
            estimatedTimeElement.textContent = Formatter.formatTime(estimatedTimeRemaining / 1000);
        }
    }

    // ===== تحديث عرض النتائج =====
    updateResultsDisplay() {
        // سيتم تنفيذها في ملف النتائج
        if (window.ResultsManager) {
            window.ResultsManager.updateDisplay(this.results);
        }
    }

    // ===== معالجة خطأ المقال =====
    handleArticleError(article, error, index) {
        console.error(`خطأ في معالجة المقال ${index + 1}:`, error);
        
        this.errors.push({
            articleId: article.id,
            title: article.title,
            filename: article.filename,
            error: error.message,
            stage: 'processing',
            index: index
        });
        
        UIHelper.showAlert(`خطأ في معالجة المقال: ${article.title}`, 'error');
    }

    // ===== معالجة خطأ المعالجة العامة =====
    handleProcessingError(error) {
        console.error('خطأ في المعالجة العامة:', error);
        
        this.isProcessing = false;
        this.updateCurrentStatus('توقفت المعالجة بسبب خطأ');
        
        UIHelper.showAlert(`خطأ في المعالجة: ${error.message}`, 'error');
    }

    // ===== إكمال المعالجة =====
    completeProcessing() {
        this.isProcessing = false;
        this.updateCurrentStatus('تم الانتهاء من المعالجة');
        this.updateProgressBar(100, 'مكتمل');
        
        // إظهار ملخص النتائج
        const successCount = this.results.length;
        const errorCount = this.errors.length;
        
        UIHelper.showAlert(
            `تم الانتهاء من المعالجة! نجح: ${successCount}، فشل: ${errorCount}`, 
            successCount > 0 ? 'success' : 'warning'
        );
        
        // التبديل إلى تبويب النتائج
        if (successCount > 0) {
            document.getElementById('results-tab').click();
        }
    }

    // ===== إيقاف المعالجة =====
    stopProcessing() {
        this.isProcessing = false;
        this.isPaused = false;
        this.updateCurrentStatus('تم إيقاف المعالجة');
    }

    // ===== إيقاف مؤقت =====
    pauseProcessing() {
        this.isPaused = !this.isPaused;
        
        if (this.isPaused) {
            this.pauseTime = Date.now();
            this.updateCurrentStatus('متوقف مؤقتاً');
        } else {
            if (this.pauseTime) {
                this.startTime += Date.now() - this.pauseTime;
            }
            this.updateCurrentStatus('جاري المعالجة');
        }
    }

    // ===== تنظيف المحتوى المحسن =====
    cleanOptimizedContent(content) {
        // إزالة النصوص التمهيدية غير المرغوب فيها
        const unwantedPhrases = [
            /بالتأكيد[!؟]?\s*إليك\s+المقال\s+المُحسَّن/gi,
            /إليك\s+المقال\s+المحسن/gi,
            /بتنسيق\s+HTML\s+مع\s+CSS\s+مدمج/gi,
            /مع\s+مراعاة\s+جميع\s+المتطلبات/gi,
            /```html/gi,
            /```/gi,
            /هنا\s+المقال\s+المحسن/gi,
            /تم\s+تحسين\s+المقال/gi,
            /المقال\s+المحسن\s+كالتالي/gi
        ];

        let cleanedContent = content;

        // إزالة العبارات غير المرغوب فيها
        unwantedPhrases.forEach(phrase => {
            cleanedContent = cleanedContent.replace(phrase, '');
        });

        // إزالة الأسطر الفارغة الزائدة
        cleanedContent = cleanedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

        // إزالة المسافات الزائدة في البداية والنهاية
        cleanedContent = cleanedContent.trim();

        // التأكد من أن المحتوى يبدأ بعنصر HTML صحيح
        if (!cleanedContent.startsWith('<')) {
            // البحث عن أول عنصر HTML
            const htmlMatch = cleanedContent.match(/<[^>]+>/);
            if (htmlMatch) {
                const startIndex = cleanedContent.indexOf(htmlMatch[0]);
                cleanedContent = cleanedContent.substring(startIndex);
            }
        }

        return cleanedContent;
    }

    // ===== الحصول على النتائج =====
    getResults() {
        return {
            results: this.results,
            errors: this.errors,
            stats: {
                total: this.totalArticles,
                processed: this.processedArticles,
                successful: this.results.length,
                failed: this.errors.length,
                processingTime: Date.now() - this.startTime
            }
        };
    }
}

// ===== إنشاء مثيل عام =====
window.ArticleProcessor = new ArticleProcessor();

console.log('تم تحميل معالج المقالات بنجاح');
