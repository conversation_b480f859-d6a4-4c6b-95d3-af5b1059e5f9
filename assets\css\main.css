/* ===== أداة تحسين المقالات للسيو - التصميم الرئيسي ===== */

/* ===== المتغيرات الأساسية ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #f97316;
    --danger-color: #dc2626;
    --success-color: #16a34a;
    --warning-color: #ca8a04;
    --info-color: #0891b2;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* ===== الخطوط الأساسية ===== */
body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* ===== شاشة التحميل ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== رأس الصفحة ===== */
.app-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: 20px 0;
    margin-bottom: 30px;
    box-shadow: var(--shadow-light);
}

.app-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 5px;
    font-size: 2rem;
}

.app-subtitle {
    color: #6b7280;
    font-size: 1.1rem;
    margin: 0;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    padding-bottom: 100px;
}

.main-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
}

.main-card .card-header {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-bottom: none;
    padding: 0;
}

.main-card .nav-tabs {
    border-bottom: none;
}

.main-card .nav-tabs .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border: none;
    padding: 15px 25px;
    font-weight: 600;
    transition: var(--transition);
    border-radius: 0;
}

.main-card .nav-tabs .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.main-card .nav-tabs .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border-bottom: 3px solid white;
}

/* ===== شريط التقدم ===== */
.progress-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.progress-header h4 {
    margin: 0;
    color: var(--dark-color);
    font-weight: 600;
}

.progress-controls .btn {
    margin-left: 10px;
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 0.9rem;
}

.progress {
    height: 25px;
    border-radius: 15px;
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--success-color), var(--info-color));
    border-radius: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: width 0.3s ease;
}

.progress-details {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.progress-details .fw-bold {
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* ===== الأزرار ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 10px 20px;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #3b82f6);
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #1d4ed8, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: linear-gradient(45deg, var(--secondary-color), #fb923c);
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #22c55e);
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #ef4444);
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #eab308);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* ===== البطاقات ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    border-bottom: none;
}

/* ===== النماذج ===== */
.form-control, .form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    transition: var(--transition);
    font-family: inherit;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* ===== التبديل والخيارات ===== */
.form-check-input {
    width: 1.5em;
    height: 1.5em;
    border: 2px solid var(--border-color);
    border-radius: 4px;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-switch .form-check-input {
    border-radius: 2em;
    width: 3em;
}

/* ===== ذيل الصفحة ===== */
.app-footer {
    background: rgba(31, 41, 55, 0.95);
    backdrop-filter: blur(10px);
    color: white;
    padding: 30px 0;
    margin-top: 50px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== التأثيرات التفاعلية ===== */
.hover-effect {
    transition: var(--transition);
}

.hover-effect:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

/* ===== الرسوم المتحركة ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* ===== التجاوب مع الشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .app-title {
        font-size: 1.5rem;
    }
    
    .app-subtitle {
        font-size: 1rem;
    }
    
    .progress-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .progress-controls {
        display: flex;
        gap: 10px;
    }
    
    .main-card .nav-tabs .nav-link {
        padding: 12px 15px;
        font-size: 0.9rem;
    }
}

/* ===== تحسينات إضافية ===== */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.shadow-custom {
    box-shadow: var(--shadow-heavy);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) 1;
}

/* ===== تحسينات النتائج ===== */
.article-result-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    margin-bottom: 20px;
}

.article-result-card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.article-result-card .card-header {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-bottom: none;
}

.optimized-preview {
    background: #f8f9fa;
    padding: 15px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    font-size: 0.95rem;
    line-height: 1.6;
}

.article-full-content {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: var(--border-radius);
}

.article-full-content h1,
.article-full-content h2,
.article-full-content h3 {
    color: var(--primary-color);
    margin-top: 25px;
    margin-bottom: 15px;
}

.article-full-content table {
    width: 100%;
    margin: 20px 0;
    border-collapse: collapse;
}

.article-full-content table th,
.article-full-content table td {
    padding: 12px;
    border: 1px solid var(--border-color);
    text-align: right;
}

.article-full-content table th {
    background: var(--light-color);
    font-weight: 600;
}

.article-full-content ul,
.article-full-content ol {
    margin: 15px 0;
    padding-right: 25px;
}

.article-full-content li {
    margin-bottom: 8px;
}

/* ===== تحسينات منطقة الرفع ===== */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    background: var(--light-color);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    transform: scale(1.02);
}

/* ===== تحسينات الإحصائيات ===== */
.stats-summary .stat-item {
    text-align: center;
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.stats-summary .stat-item strong {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
}

.stats-summary .stat-item small {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* ===== تحسينات الأزرار ===== */
.btn-group .btn {
    margin-left: 5px;
}

.btn-group .btn:first-child {
    margin-left: 0;
}

/* ===== تحسينات النماذج ===== */
.form-check-input:checked[type=checkbox] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
}

/* ===== تحسينات الجداول ===== */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table thead th {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background: rgba(37, 99, 235, 0.05);
}

/* ===== تحسينات التنبيهات ===== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.alert-success {
    background: linear-gradient(45deg, rgba(22, 163, 74, 0.1), rgba(34, 197, 94, 0.1));
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(45deg, rgba(220, 38, 38, 0.1), rgba(239, 68, 68, 0.1));
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(45deg, rgba(202, 138, 4, 0.1), rgba(234, 179, 8, 0.1));
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(45deg, rgba(8, 145, 178, 0.1), rgba(14, 165, 233, 0.1));
    border-left: 4px solid var(--info-color);
}

/* ===== تنسيقات متقدمة للمحتوى المحسن ===== */

/* خطوط مختلفة */
.font-tajawal { font-family: 'Tajawal', sans-serif; }
.font-cairo { font-family: 'Cairo', sans-serif; }
.font-amiri { font-family: 'Amiri', serif; }
.font-noto { font-family: 'Noto Sans Arabic', sans-serif; }

/* أنماط العناوين المتقدمة */
.heading-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.heading-underline {
    position: relative;
    padding-bottom: 10px;
}

.heading-underline::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.heading-border-left {
    border-right: 4px solid var(--primary-color);
    padding-right: 15px;
    margin-right: 10px;
}

.heading-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.heading-highlight {
    position: relative;
    padding: 5px 10px;
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.05));
    border-radius: 5px;
}

.heading-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px;
}

/* تأثيرات المرور على العناوين */
.heading-gradient:hover,
.heading-underline:hover,
.heading-border-left:hover,
.heading-shadow:hover,
.heading-highlight:hover {
    transform: translateX(-5px);
    transition: var(--transition);
}

/* أنماط الجداول المتقدمة */
.table-modern {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.table-modern thead th {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-modern tbody tr:nth-child(even) {
    background: rgba(37, 99, 235, 0.03);
}

.table-modern tbody tr:hover {
    background: rgba(37, 99, 235, 0.08);
    transform: scale(1.01);
    transition: var(--transition);
}

.table-classic {
    border: 2px solid var(--border-color);
}

.table-classic thead th {
    background: var(--light-color);
    color: var(--dark-color);
    border-bottom: 2px solid var(--primary-color);
}

.table-minimal {
    border: none;
}

.table-minimal thead th {
    background: none;
    color: var(--dark-color);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.table-minimal tbody td {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table-colorful thead th:nth-child(odd) {
    background: var(--primary-color);
    color: white;
}

.table-colorful thead th:nth-child(even) {
    background: var(--secondary-color);
    color: white;
}

.table-colorful tbody tr:nth-child(odd) {
    background: rgba(37, 99, 235, 0.05);
}

.table-colorful tbody tr:nth-child(even) {
    background: rgba(249, 115, 22, 0.05);
}

/* أنماط القوائم المحسنة */
.list-enhanced ul {
    list-style: none;
    padding-right: 0;
}

.list-enhanced li {
    position: relative;
    padding: 10px 0 10px 30px;
    margin-bottom: 8px;
    background: rgba(37, 99, 235, 0.03);
    border-radius: 5px;
    padding-right: 40px;
    transition: var(--transition);
}

.list-enhanced li:hover {
    background: rgba(37, 99, 235, 0.08);
    transform: translateX(-5px);
}

.list-enhanced li::before {
    content: '✓';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--success-color);
    font-weight: bold;
    font-size: 1.2em;
}

.list-boxed ul {
    list-style: none;
    padding-right: 0;
}

.list-boxed li {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: var(--shadow-light);
    position: relative;
    padding-right: 50px;
}

.list-boxed li::before {
    content: counter(list-counter);
    counter-increment: list-counter;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9em;
}

.list-boxed ul {
    counter-reset: list-counter;
}

/* أنماط الاقتباسات المحسنة */
.quote-modern {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(59, 130, 246, 0.02));
    border-right: 4px solid var(--primary-color);
    padding: 20px 25px;
    margin: 20px 0;
    border-radius: 8px;
    position: relative;
    font-style: italic;
    box-shadow: var(--shadow-light);
}

.quote-modern::before {
    content: '"';
    position: absolute;
    top: -10px;
    right: 15px;
    font-size: 3em;
    color: var(--primary-color);
    opacity: 0.3;
    font-family: serif;
}

.quote-classic {
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-right: 5px solid var(--secondary-color);
    padding: 15px 20px;
    margin: 15px 0;
    font-style: italic;
    color: var(--dark-color);
}

.quote-minimal {
    border-right: 2px solid var(--border-color);
    padding-right: 20px;
    margin: 15px 0;
    font-style: italic;
    color: #6b7280;
}

/* ===== تحسينات عرض النتائج ===== */
.improvements-list {
    background: rgba(22, 163, 74, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
}

.improvement-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.improvement-item:not(:last-child) {
    border-bottom: 1px solid rgba(22, 163, 74, 0.1);
    margin-bottom: 8px;
    padding-bottom: 8px;
}

.keywords-display {
    margin-top: 8px;
}

.keywords-display .badge {
    font-size: 0.8rem;
    padding: 5px 10px;
    margin-bottom: 5px;
}

/* تحسينات إضافية للبطاقات */
.article-result-card .card-body {
    padding: 25px;
}

.optimized-preview {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.8), rgba(241, 245, 249, 0.6));
    border: 1px solid rgba(37, 99, 235, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.optimized-preview::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
}

/* تحسينات الإحصائيات */
.article-result-card h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 0.95rem;
}

.article-result-card ul.list-unstyled li {
    padding: 3px 0;
    font-size: 0.85rem;
}

.article-result-card .small {
    line-height: 1.5;
}

/* تحسينات الأزرار */
.article-result-card .btn-sm {
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: 6px;
    margin-left: 8px;
    transition: var(--transition);
}

.article-result-card .btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* تحسينات الشارات */
.badge {
    font-weight: 500;
    letter-spacing: 0.3px;
}

.badge.bg-success {
    background: linear-gradient(45deg, var(--success-color), #22c55e) !important;
}

.badge.bg-warning {
    background: linear-gradient(45deg, var(--warning-color), #eab308) !important;
}

.badge.bg-danger {
    background: linear-gradient(45deg, var(--danger-color), #ef4444) !important;
}

.badge.bg-info {
    background: linear-gradient(45deg, var(--info-color), #14b8a6) !important;
}

/* ===== تحسينات تحليل الكلمات المفتاحية ===== */
.keyword-analysis {
    background: rgba(37, 99, 235, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
    border: 1px solid rgba(37, 99, 235, 0.1);
}

.primary-keyword {
    background: rgba(37, 99, 235, 0.1);
    padding: 10px;
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
}

.keyword-locations {
    padding: 5px 10px;
    background: rgba(8, 145, 178, 0.05);
    border-radius: 4px;
}

.secondary-keywords {
    padding: 8px;
    background: rgba(107, 114, 128, 0.05);
    border-radius: 4px;
}

.badge.bg-outline-secondary {
    background: transparent !important;
    border: 1px solid #6b7280;
    color: #6b7280;
}

/* تحسينات إضافية للتحليل المفصل */
.improvement-item {
    transition: var(--transition);
}

.improvement-item:hover {
    background: rgba(22, 163, 74, 0.08);
    border-radius: 4px;
    padding: 5px 8px;
    margin: 0 -8px;
}

.improvements-list {
    max-height: 300px;
    overflow-y: auto;
}

.improvements-list::-webkit-scrollbar {
    width: 4px;
}

.improvements-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.improvements-list::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 2px;
}

/* تحسينات عرض الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.stat-item-small {
    text-align: center;
    padding: 8px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-item-small .stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    display: block;
}

.stat-item-small .stat-label {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 2px;
}

/* ===== تحسينات التحليل المفصل ===== */
.analysis-sections {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
}

.analysis-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid var(--primary-color);
}

.analysis-section h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.stats-comparison .stat-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: var(--shadow-light);
}

.stats-comparison .stat-card.original {
    border-left: 4px solid #dc2626;
}

.stats-comparison .stat-card.optimized {
    border-left: 4px solid #16a34a;
}

.keyword-detailed-analysis {
    background: white;
    border-radius: 8px;
    padding: 15px;
}

.primary-keyword-detailed {
    border: 1px solid rgba(37, 99, 235, 0.2);
    border-radius: 8px;
    padding: 15px;
    background: rgba(37, 99, 235, 0.02);
}

.keyword-header h6 {
    color: var(--primary-color);
    margin: 8px 0;
    font-size: 1.1rem;
}

.secondary-keywords-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.secondary-keyword-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
}

.secondary-keyword-item .keyword-name {
    display: block;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.secondary-keyword-item .keyword-count,
.secondary-keyword-item .keyword-density {
    display: inline-block;
    font-size: 0.85rem;
    color: #6b7280;
    margin: 0 5px;
}

.improvements-breakdown {
    background: white;
    border-radius: 8px;
    padding: 15px;
}

.improvement-category {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 15px;
}

.improvement-category:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.improvement-category h6 {
    color: var(--dark-color);
    margin-bottom: 10px;
    font-size: 1rem;
}

.improvement-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.improvement-list li {
    padding: 5px 0;
    padding-right: 20px;
    position: relative;
    font-size: 0.9rem;
    line-height: 1.5;
}

.improvement-list li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
}

.recommendations-list {
    background: white;
    border-radius: 8px;
    padding: 15px;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    padding: 8px 0;
    border-bottom: 1px solid rgba(249, 115, 22, 0.1);
    font-size: 0.9rem;
    line-height: 1.5;
}

.recommendation-item:last-child {
    border-bottom: none;
}

.original-content-display {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.original-text {
    background: white;
    border-radius: 6px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    max-height: 60vh;
    overflow-y: auto;
}

/* تحسينات التبويبات */
.nav-tabs .nav-link {
    color: #6b7280;
    border: none;
    border-bottom: 2px solid transparent;
    background: none;
    padding: 10px 15px;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: none;
}

.nav-tabs .nav-link:hover {
    color: var(--primary-color);
    border-bottom-color: rgba(37, 99, 235, 0.3);
}

/* تحسينات النافذة المنبثقة */
.modal-xl {
    max-width: 90%;
}

.modal-body {
    max-height: 80vh;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
        margin: 10px;
    }

    .stats-comparison .row {
        margin: 0;
    }

    .stats-comparison .col-md-6 {
        padding: 5px;
    }

    .secondary-keywords-grid {
        grid-template-columns: 1fr;
    }
}
