/* ===== أداة تحسين المقالات للسيو - التصميم الرئيسي ===== */

/* ===== المتغيرات الأساسية ===== */
:root {
    --primary-color: #1e40af;
    --secondary-color: #3b82f6;
    --danger-color: #dc2626;
    --success-color: #16a34a;
    --warning-color: #ca8a04;
    --info-color: #0891b2;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* ===== الخطوط الأساسية ===== */
body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* ===== شاشة التحميل المحسنة ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e40af 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
    overflow: hidden;
}

.loading-screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: gridMove 20s linear infinite;
}

.loading-content {
    text-align: center;
    color: white;
    position: relative;
    z-index: 2;
    max-width: 500px;
    padding: 40px;
    animation: fadeInUp 1s ease-out;
}

.loading-logo {
    margin-bottom: 30px;
}

.loading-logo i {
    font-size: 4rem;
    color: #60a5fa;
    animation: pulse 2s ease-in-out infinite;
    filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.5));
}

.loading-spinner {
    width: 80px;
    height: 80px;
    border: 6px solid rgba(255, 255, 255, 0.2);
    border-top: 6px solid #60a5fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 30px;
    filter: drop-shadow(0 0 10px rgba(96, 165, 250, 0.3));
}

.loading-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #ffffff, #60a5fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-content p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

.loading-progress {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 30px;
}

.loading-bar {
    height: 100%;
    background: linear-gradient(90deg, #60a5fa, #3b82f6, #1d4ed8);
    border-radius: 3px;
    animation: loadingProgress 3s ease-in-out infinite;
}

.loading-features {
    text-align: right;
    margin-top: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 10px;
    opacity: 0;
    animation: fadeInRight 0.5s ease-out forwards;
}

.feature-item:nth-child(1) { animation-delay: 1s; }
.feature-item:nth-child(2) { animation-delay: 1.5s; }
.feature-item:nth-child(3) { animation-delay: 2s; }

.feature-item i {
    color: #10b981;
    margin-left: 10px;
    font-size: 1.1rem;
}

.feature-item span {
    font-size: 0.95rem;
    opacity: 0.9;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes loadingProgress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

/* ===== رأس الصفحة المحسن ===== */
.app-header-premium {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(30, 64, 175, 0.1);
    padding: 25px 0;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.header-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
}

.logo-icon i {
    font-size: 2rem;
    color: white;
}

.logo-text .app-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-text .app-subtitle {
    color: #6b7280;
    font-size: 0.95rem;
    margin: 5px 0 0 0;
    font-weight: 500;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.btn-premium {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
    color: white;
}

.btn-premium-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 10px 22px;
    border-radius: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-premium-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

.header-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    padding: 20px;
    background: rgba(30, 64, 175, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(30, 64, 175, 0.1);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    min-width: 150px;
}

.stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: 0.85rem;
    color: #6b7280;
    font-weight: 500;
    margin-top: 2px;
}

/* الهيدر القديم للتوافق */
.app-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: 20px 0;
    margin-bottom: 30px;
    box-shadow: var(--shadow-light);
}

.app-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 5px;
    font-size: 2rem;
}

.app-subtitle {
    color: #6b7280;
    font-size: 1.1rem;
    margin: 0;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    padding-bottom: 100px;
}

.main-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
}

.main-card .card-header {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-bottom: none;
    padding: 0;
}

.main-card .nav-tabs {
    border-bottom: none;
}

.main-card .nav-tabs .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border: none;
    padding: 15px 25px;
    font-weight: 600;
    transition: var(--transition);
    border-radius: 0;
}

.main-card .nav-tabs .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.main-card .nav-tabs .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border-bottom: 3px solid white;
}

/* ===== شريط التقدم ===== */
.progress-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.progress-header h4 {
    margin: 0;
    color: var(--dark-color);
    font-weight: 600;
}

.progress-controls .btn {
    margin-left: 10px;
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 0.9rem;
}

.progress {
    height: 25px;
    border-radius: 15px;
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--success-color), var(--info-color));
    border-radius: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: width 0.3s ease;
}

.progress-details {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.progress-details .fw-bold {
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* ===== الأزرار ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 10px 20px;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #3b82f6);
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #1d4ed8, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: linear-gradient(45deg, var(--secondary-color), #fb923c);
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #22c55e);
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #ef4444);
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #eab308);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* ===== البطاقات ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    border-bottom: none;
}

/* ===== النماذج ===== */
.form-control, .form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    transition: var(--transition);
    font-family: inherit;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* ===== التبديل والخيارات ===== */
.form-check-input {
    width: 1.5em;
    height: 1.5em;
    border: 2px solid var(--border-color);
    border-radius: 4px;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-switch .form-check-input {
    border-radius: 2em;
    width: 3em;
}

/* ===== ذيل الصفحة ===== */
.app-footer {
    background: rgba(31, 41, 55, 0.95);
    backdrop-filter: blur(10px);
    color: white;
    padding: 30px 0;
    margin-top: 50px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== التأثيرات التفاعلية ===== */
.hover-effect {
    transition: var(--transition);
}

.hover-effect:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

/* ===== الرسوم المتحركة ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* ===== التجاوب مع الشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .app-title {
        font-size: 1.5rem;
    }
    
    .app-subtitle {
        font-size: 1rem;
    }
    
    .progress-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .progress-controls {
        display: flex;
        gap: 10px;
    }
    
    .main-card .nav-tabs .nav-link {
        padding: 12px 15px;
        font-size: 0.9rem;
    }
}

/* ===== تحسينات إضافية ===== */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.shadow-custom {
    box-shadow: var(--shadow-heavy);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) 1;
}

/* ===== تحسينات النتائج ===== */
.article-result-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    margin-bottom: 20px;
}

.article-result-card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.article-result-card .card-header {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-bottom: none;
}

.optimized-preview {
    background: #f8f9fa;
    padding: 15px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    font-size: 0.95rem;
    line-height: 1.6;
}

.article-full-content {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: var(--border-radius);
}

.article-full-content h1,
.article-full-content h2,
.article-full-content h3 {
    color: var(--primary-color);
    margin-top: 25px;
    margin-bottom: 15px;
}

.article-full-content table {
    width: 100%;
    margin: 20px 0;
    border-collapse: collapse;
}

.article-full-content table th,
.article-full-content table td {
    padding: 12px;
    border: 1px solid var(--border-color);
    text-align: right;
}

.article-full-content table th {
    background: var(--light-color);
    font-weight: 600;
}

.article-full-content ul,
.article-full-content ol {
    margin: 15px 0;
    padding-right: 25px;
}

.article-full-content li {
    margin-bottom: 8px;
}

/* ===== تحسينات منطقة الرفع ===== */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    background: var(--light-color);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    transform: scale(1.02);
}

/* ===== تحسينات الإحصائيات ===== */
.stats-summary .stat-item {
    text-align: center;
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.stats-summary .stat-item strong {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
}

.stats-summary .stat-item small {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* ===== تحسينات الأزرار ===== */
.btn-group .btn {
    margin-left: 5px;
}

.btn-group .btn:first-child {
    margin-left: 0;
}

/* ===== تحسينات النماذج ===== */
.form-check-input:checked[type=checkbox] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
}

/* ===== تحسينات الجداول ===== */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table thead th {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background: rgba(37, 99, 235, 0.05);
}

/* ===== تحسينات التنبيهات ===== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.alert-success {
    background: linear-gradient(45deg, rgba(22, 163, 74, 0.1), rgba(34, 197, 94, 0.1));
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(45deg, rgba(220, 38, 38, 0.1), rgba(239, 68, 68, 0.1));
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(45deg, rgba(202, 138, 4, 0.1), rgba(234, 179, 8, 0.1));
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(45deg, rgba(8, 145, 178, 0.1), rgba(14, 165, 233, 0.1));
    border-left: 4px solid var(--info-color);
}

/* ===== تنسيقات متقدمة للمحتوى المحسن ===== */

/* خطوط مختلفة */
.font-tajawal { font-family: 'Tajawal', sans-serif; }
.font-cairo { font-family: 'Cairo', sans-serif; }
.font-amiri { font-family: 'Amiri', serif; }
.font-noto { font-family: 'Noto Sans Arabic', sans-serif; }

/* أنماط العناوين المتقدمة */
.heading-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.heading-underline {
    position: relative;
    padding-bottom: 10px;
}

.heading-underline::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* خط سفلي للكلمة الأولى فقط */
.heading-underline-first-word {
    position: relative;
    display: inline-block;
}

.heading-underline-first-word .first-word {
    position: relative;
    display: inline-block;
}

.heading-underline-first-word .first-word::after {
    content: '';
    position: absolute;
    bottom: -4px;
    right: 0;
    left: 0;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
    transition: var(--transition);
}

.heading-underline-first-word {
    position: relative;
}

.heading-underline-first-word::after {
    content: '';
    position: absolute;
    bottom: -3px;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
    transition: var(--transition);
}

/* تحديد عرض الخط السفلي للكلمة الأولى ديناميكياً */
.heading-underline-first-word[data-first-word-length="1"]::after { width: 1.5em; }
.heading-underline-first-word[data-first-word-length="2"]::after { width: 2.5em; }
.heading-underline-first-word[data-first-word-length="3"]::after { width: 3.5em; }
.heading-underline-first-word[data-first-word-length="4"]::after { width: 4.5em; }
.heading-underline-first-word[data-first-word-length="5"]::after { width: 5.5em; }
.heading-underline-first-word[data-first-word-length="6"]::after { width: 6.5em; }
.heading-underline-first-word[data-first-word-length="7"]::after { width: 7.5em; }
.heading-underline-first-word[data-first-word-length="8"]::after { width: 8.5em; }
.heading-underline-first-word[data-first-word-length="9"]::after { width: 9.5em; }
.heading-underline-first-word[data-first-word-length="10"]::after { width: 10.5em; }

.heading-border-left {
    border-right: 4px solid var(--primary-color);
    padding-right: 15px;
    margin-right: 10px;
}

.heading-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.heading-highlight {
    position: relative;
    padding: 5px 10px;
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.05));
    border-radius: 5px;
}

.heading-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px;
}

/* تأثيرات المرور على العناوين */
.heading-gradient:hover,
.heading-underline:hover,
.heading-underline-first-word:hover,
.heading-border-left:hover,
.heading-shadow:hover,
.heading-highlight:hover {
    transform: translateX(-5px);
    transition: var(--transition);
}

/* ===== تمييز الكلمات المفتاحية ===== */
.keyword-bold {
    font-weight: 700;
    color: inherit;
}

.keyword-color {
    color: var(--primary-color);
    font-weight: 600;
}

.keyword-background {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.05));
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 500;
}

.keyword-underline {
    text-decoration: underline;
    text-decoration-color: var(--primary-color);
    text-decoration-thickness: 2px;
    text-underline-offset: 2px;
    font-weight: 500;
}

/* ألوان العناوين المخصصة */
.heading-color-primary { color: var(--primary-color) !important; }
.heading-color-secondary { color: var(--secondary-color) !important; }
.heading-color-custom { color: var(--heading-color, #1e40af) !important; }
.subheading-color-custom { color: var(--subheading-color, #3b82f6) !important; }

/* أنماط الجداول المتقدمة */
.table-modern {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.table-modern thead th {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-modern tbody tr:nth-child(even) {
    background: rgba(37, 99, 235, 0.03);
}

.table-modern tbody tr:hover {
    background: rgba(37, 99, 235, 0.08);
    transform: scale(1.01);
    transition: var(--transition);
}

.table-classic {
    border: 2px solid var(--border-color);
}

.table-classic thead th {
    background: var(--light-color);
    color: var(--dark-color);
    border-bottom: 2px solid var(--primary-color);
}

.table-minimal {
    border: none;
}

.table-minimal thead th {
    background: none;
    color: var(--dark-color);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.table-minimal tbody td {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table-colorful thead th:nth-child(odd) {
    background: var(--primary-color);
    color: white;
}

.table-colorful thead th:nth-child(even) {
    background: var(--secondary-color);
    color: white;
}

.table-colorful tbody tr:nth-child(odd) {
    background: rgba(37, 99, 235, 0.05);
}

.table-colorful tbody tr:nth-child(even) {
    background: rgba(249, 115, 22, 0.05);
}

/* أنماط القوائم المحسنة */
.list-enhanced ul {
    list-style: none;
    padding-right: 0;
}

.list-enhanced li {
    position: relative;
    padding: 10px 0 10px 30px;
    margin-bottom: 8px;
    background: rgba(37, 99, 235, 0.03);
    border-radius: 5px;
    padding-right: 40px;
    transition: var(--transition);
}

.list-enhanced li:hover {
    background: rgba(37, 99, 235, 0.08);
    transform: translateX(-5px);
}

.list-enhanced li::before {
    content: '✓';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--success-color);
    font-weight: bold;
    font-size: 1.2em;
}

.list-boxed ul {
    list-style: none;
    padding-right: 0;
}

.list-boxed li {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: var(--shadow-light);
    position: relative;
    padding-right: 50px;
}

.list-boxed li::before {
    content: counter(list-counter);
    counter-increment: list-counter;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9em;
}

.list-boxed ul {
    counter-reset: list-counter;
}

/* أنماط القوائم الجديدة */
.list-highlighted ul {
    list-style: none;
    padding-right: 0;
}

.list-highlighted li {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.05));
    border-left: 4px solid var(--primary-color);
    padding: 12px 20px;
    margin-bottom: 8px;
    border-radius: 6px;
    position: relative;
    transition: var(--transition);
}

.list-highlighted li:hover {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.15), rgba(59, 130, 246, 0.08));
    transform: translateX(-3px);
}

.list-highlighted li::before {
    content: '▶';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 0.8em;
}

.list-quote-style ul {
    list-style: none;
    padding-right: 0;
}

.list-quote-style li {
    background: rgba(248, 249, 250, 0.8);
    border: 1px solid rgba(37, 99, 235, 0.2);
    border-radius: 8px;
    padding: 15px 25px;
    margin-bottom: 12px;
    position: relative;
    font-style: italic;
    box-shadow: var(--shadow-light);
}

.list-quote-style li::before {
    content: '"';
    position: absolute;
    top: -5px;
    right: 10px;
    font-size: 2em;
    color: var(--primary-color);
    opacity: 0.3;
    font-family: serif;
}

.list-numbered-boxes ul {
    list-style: none;
    padding-right: 0;
    counter-reset: numbered-counter;
}

.list-numbered-boxes li {
    background: white;
    border: 2px solid var(--primary-color);
    border-radius: 10px;
    padding: 15px 60px 15px 20px;
    margin-bottom: 15px;
    position: relative;
    box-shadow: var(--shadow-medium);
    counter-increment: numbered-counter;
}

.list-numbered-boxes li::before {
    content: counter(numbered-counter);
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1em;
}

/* أنماط محاذاة الجداول */
.table-align-center td,
.table-align-center th {
    text-align: center;
}

.table-align-right td,
.table-align-right th {
    text-align: right;
}

.table-align-left td,
.table-align-left th {
    text-align: left;
}

/* أنماط اتجاه النص */
.text-rtl-justify {
    direction: rtl;
    text-align: justify;
}

.text-rtl {
    direction: rtl;
    text-align: right;
}

.text-ltr {
    direction: ltr;
    text-align: left;
}

.text-center-justify {
    text-align: justify;
    margin: 0 auto;
    max-width: 90%;
}

.text-center {
    text-align: center;
}

/* تنسيق حقوق النشر */
.copyright-section {
    background: rgba(248, 249, 250, 0.9);
    border-top: 2px solid var(--primary-color);
    padding: 20px;
    margin-top: 30px;
    text-align: center;
    font-size: 0.9rem;
    color: #6b7280;
    border-radius: 8px;
}

.copyright-section .copyright-icon {
    color: var(--primary-color);
    margin-left: 5px;
}

/* تنسيق الخاتمة المميزة */
.conclusion-section {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(59, 130, 246, 0.02));
    border: 1px solid rgba(37, 99, 235, 0.1);
    border-radius: 12px;
    padding: 25px;
    margin-top: 30px;
    position: relative;
}

.conclusion-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px 12px 0 0;
}

.conclusion-section h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.conclusion-section .conclusion-icon {
    color: var(--primary-color);
    margin-left: 10px;
    font-size: 1.2em;
}

/* ===== واجهة الإعدادات المحسنة ===== */
.options-container-premium {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.options-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.options-title {
    display: flex;
    align-items: center;
    gap: 20px;
}

.title-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
}

.title-content h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.title-content p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
}

.options-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn-premium-sm {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-premium-sm:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
}

.btn-premium-outline-sm {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.5);
    color: white;
    padding: 8px 18px;
    border-radius: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-premium-outline-sm:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: white;
    transform: translateY(-2px);
    color: white;
}

.options-tabs {
    background: #f8fafc;
    padding: 0;
    border-bottom: 1px solid #e2e8f0;
}

.nav-premium {
    border: none;
    padding: 0;
    display: flex;
    overflow-x: auto;
}

.nav-premium .nav-link {
    background: transparent;
    border: none;
    color: #6b7280;
    padding: 20px 30px;
    border-radius: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.nav-premium .nav-link:hover {
    color: var(--primary-color);
    background: rgba(30, 64, 175, 0.05);
}

.nav-premium .nav-link.active {
    color: var(--primary-color);
    background: white;
    border-bottom-color: var(--primary-color);
}

.options-content {
    padding: 40px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.setting-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 15px;
    padding: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.setting-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.setting-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.setting-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
}

.setting-title h5 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--dark-color);
}

.setting-title p {
    margin: 5px 0 0 0;
    color: #6b7280;
    font-size: 0.9rem;
}

.setting-content {
    margin-top: 15px;
}

.form-select-premium,
.form-control-premium {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 12px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.form-select-premium:focus,
.form-control-premium:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    background: white;
}

.input-group-text {
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
    font-weight: 600;
}

/* ===== واجهة رفع الملفات المحسنة ===== */
.upload-container-premium {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.upload-header {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.upload-title {
    display: flex;
    align-items: center;
    gap: 20px;
}

.upload-stats {
    display: flex;
    gap: 15px;
}

.stat-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 10px 15px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.stat-badge i {
    font-size: 1.2rem;
}

.stat-badge span {
    font-size: 1.3rem;
    font-weight: 700;
}

.stat-badge small {
    opacity: 0.9;
    font-size: 0.8rem;
}

.upload-methods {
    padding: 0;
}

.method-tabs {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.method-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-weight: 600;
    color: #6b7280;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.method-tab:hover {
    color: var(--primary-color);
    background: rgba(30, 64, 175, 0.05);
}

.method-tab.active {
    color: var(--primary-color);
    background: white;
    border-bottom-color: var(--primary-color);
}

.method-content {
    padding: 40px;
}

.method-panel {
    display: none;
}

.method-panel.active {
    display: block;
}

.upload-zone {
    border: 3px dashed #d1d5db;
    border-radius: 15px;
    padding: 60px 40px;
    text-align: center;
    transition: all 0.3s ease;
    background: #f9fafb;
    cursor: pointer;
}

.upload-zone:hover {
    border-color: var(--primary-color);
    background: rgba(30, 64, 175, 0.02);
}

.upload-zone.dragover {
    border-color: var(--primary-color);
    background: rgba(30, 64, 175, 0.05);
    transform: scale(1.02);
}

.upload-icon {
    margin-bottom: 20px;
}

.upload-icon i {
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.7;
}

.upload-zone h4 {
    color: var(--dark-color);
    margin-bottom: 10px;
    font-weight: 600;
}

.upload-zone p {
    color: #6b7280;
    margin-bottom: 30px;
}

.files-list {
    margin-top: 30px;
    padding: 25px;
    background: #f8fafc;
    border-radius: 15px;
    border: 1px solid #e2e8f0;
}

.files-list h5 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-weight: 600;
}

.files-container {
    display: grid;
    gap: 15px;
}

.file-item {
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.file-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.file-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.file-details h6 {
    margin: 0;
    font-weight: 600;
    color: var(--dark-color);
}

.file-details small {
    color: #6b7280;
}

.file-actions {
    display: flex;
    gap: 10px;
}

.paste-zone,
.url-zone {
    background: #f9fafb;
    border-radius: 15px;
    padding: 30px;
}

.paste-header,
.url-header {
    text-align: center;
    margin-bottom: 30px;
}

.paste-header h4,
.url-header h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-weight: 600;
}

.paste-content textarea {
    resize: vertical;
    min-height: 200px;
}

.paste-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin: 20px 0;
}

.paste-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    padding: 15px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.paste-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-weight: 500;
}

.url-options {
    margin: 20px 0;
    display: flex;
    gap: 30px;
    justify-content: center;
}

.url-preview {
    margin-top: 25px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.preview-content {
    max-height: 200px;
    overflow-y: auto;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    margin: 15px 0;
    font-size: 0.9rem;
    line-height: 1.6;
}

.input-group-premium {
    margin-bottom: 20px;
}

.input-group-premium .form-control {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.input-group-premium .btn {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

/* ===== تحسينات الاستجابة للأجهزة المختلفة ===== */
@media (max-width: 768px) {
    .header-main {
        flex-direction: column;
        text-align: center;
    }

    .header-stats {
        flex-direction: column;
        gap: 15px;
    }

    .stat-item {
        min-width: auto;
        justify-content: center;
    }

    .options-header {
        flex-direction: column;
        text-align: center;
    }

    .options-title {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .method-tabs {
        flex-direction: column;
    }

    .method-tab {
        border-bottom: 1px solid #e2e8f0;
        border-radius: 0;
    }

    .upload-zone {
        padding: 40px 20px;
    }

    .upload-icon i {
        font-size: 3rem;
    }

    .paste-actions {
        flex-direction: column;
    }

    .paste-stats {
        flex-direction: column;
        gap: 15px;
    }

    .url-options {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .loading-content {
        padding: 20px;
    }

    .loading-content h2 {
        font-size: 1.5rem;
    }

    .loading-logo i {
        font-size: 3rem;
    }

    .logo-icon {
        width: 50px;
        height: 50px;
    }

    .logo-icon i {
        font-size: 1.5rem;
    }

    .logo-text .app-title {
        font-size: 1.4rem;
    }

    .setting-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .setting-icon {
        width: 40px;
        height: 40px;
    }
}

/* ===== تحسينات إضافية للأداء ===== */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

/* تحسينات الأزرار */
.btn {
    transition: all 0.3s ease;
    font-weight: 600;
    border-radius: 10px;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

/* تحسينات البطاقات */
.card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* تحسينات النماذج */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

/* تحسينات التنبيهات */
.alert {
    border: none;
    border-radius: 12px;
    padding: 15px 20px;
    font-weight: 500;
}

.alert-info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.05));
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
}

.alert-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(134, 239, 172, 0.05));
    color: #059669;
    border-left: 4px solid #059669;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(253, 230, 138, 0.05));
    color: #d97706;
    border-left: 4px solid #d97706;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(252, 165, 165, 0.05));
    color: #dc2626;
    border-left: 4px solid #dc2626;
}

/* ===== نافذة المساعدة المحسنة ===== */
.modal-premium .modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.modal-header-premium {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 25px 30px;
    border-bottom: none;
}

.modal-title-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.modal-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.modal-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.modal-subtitle {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 0.95rem;
}

.help-content {
    padding: 20px 0;
}

.help-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
}

.help-section h6 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.help-section ul {
    margin: 0;
    padding-right: 20px;
}

.help-section li {
    margin-bottom: 8px;
    line-height: 1.6;
    color: #4b5563;
}

.help-section li strong {
    color: var(--primary-color);
    font-weight: 600;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

/* تحسينات إضافية للنوافذ المنبثقة */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
}

.modal.fade .modal-dialog {
    transform: translate(0, -50px);
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: translate(0, 0);
}

/* أنماط الاقتباسات المحسنة */
.quote-modern {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(59, 130, 246, 0.02));
    border-right: 4px solid var(--primary-color);
    padding: 20px 25px;
    margin: 20px 0;
    border-radius: 8px;
    position: relative;
    font-style: italic;
    box-shadow: var(--shadow-light);
}

.quote-modern::before {
    content: '"';
    position: absolute;
    top: -10px;
    right: 15px;
    font-size: 3em;
    color: var(--primary-color);
    opacity: 0.3;
    font-family: serif;
}

.quote-classic {
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-right: 5px solid var(--secondary-color);
    padding: 15px 20px;
    margin: 15px 0;
    font-style: italic;
    color: var(--dark-color);
}

.quote-minimal {
    border-right: 2px solid var(--border-color);
    padding-right: 20px;
    margin: 15px 0;
    font-style: italic;
    color: #6b7280;
}

/* ===== تحسينات عرض النتائج ===== */
.improvements-list {
    background: rgba(22, 163, 74, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
}

.improvement-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.improvement-item:not(:last-child) {
    border-bottom: 1px solid rgba(22, 163, 74, 0.1);
    margin-bottom: 8px;
    padding-bottom: 8px;
}

.keywords-display {
    margin-top: 8px;
}

.keywords-display .badge {
    font-size: 0.8rem;
    padding: 5px 10px;
    margin-bottom: 5px;
}

/* تحسينات إضافية للبطاقات */
.article-result-card .card-body {
    padding: 25px;
}

.optimized-preview {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.8), rgba(241, 245, 249, 0.6));
    border: 1px solid rgba(37, 99, 235, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.optimized-preview::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
}

/* تحسينات الإحصائيات */
.article-result-card h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 0.95rem;
}

.article-result-card ul.list-unstyled li {
    padding: 3px 0;
    font-size: 0.85rem;
}

.article-result-card .small {
    line-height: 1.5;
}

/* تحسينات الأزرار */
.article-result-card .btn-sm {
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: 6px;
    margin-left: 8px;
    transition: var(--transition);
}

.article-result-card .btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* تحسينات الشارات */
.badge {
    font-weight: 500;
    letter-spacing: 0.3px;
}

.badge.bg-success {
    background: linear-gradient(45deg, var(--success-color), #22c55e) !important;
}

.badge.bg-warning {
    background: linear-gradient(45deg, var(--warning-color), #eab308) !important;
}

.badge.bg-danger {
    background: linear-gradient(45deg, var(--danger-color), #ef4444) !important;
}

.badge.bg-info {
    background: linear-gradient(45deg, var(--info-color), #14b8a6) !important;
}

/* ===== تحسينات تحليل الكلمات المفتاحية ===== */
.keyword-analysis {
    background: rgba(37, 99, 235, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
    border: 1px solid rgba(37, 99, 235, 0.1);
}

.primary-keyword {
    background: rgba(37, 99, 235, 0.1);
    padding: 10px;
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
}

.keyword-locations {
    padding: 5px 10px;
    background: rgba(8, 145, 178, 0.05);
    border-radius: 4px;
}

.secondary-keywords {
    padding: 8px;
    background: rgba(107, 114, 128, 0.05);
    border-radius: 4px;
}

.badge.bg-outline-secondary {
    background: transparent !important;
    border: 1px solid #6b7280;
    color: #6b7280;
}

/* تحسينات إضافية للتحليل المفصل */
.improvement-item {
    transition: var(--transition);
}

.improvement-item:hover {
    background: rgba(22, 163, 74, 0.08);
    border-radius: 4px;
    padding: 5px 8px;
    margin: 0 -8px;
}

.improvements-list {
    max-height: 300px;
    overflow-y: auto;
}

.improvements-list::-webkit-scrollbar {
    width: 4px;
}

.improvements-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.improvements-list::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 2px;
}

/* تحسينات عرض الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.stat-item-small {
    text-align: center;
    padding: 8px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-item-small .stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    display: block;
}

.stat-item-small .stat-label {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 2px;
}

/* ===== تحسينات التحليل المفصل ===== */
.analysis-sections {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
}

.analysis-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid var(--primary-color);
}

.analysis-section h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.stats-comparison .stat-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: var(--shadow-light);
}

.stats-comparison .stat-card.original {
    border-left: 4px solid #dc2626;
}

.stats-comparison .stat-card.optimized {
    border-left: 4px solid #16a34a;
}

.keyword-detailed-analysis {
    background: white;
    border-radius: 8px;
    padding: 15px;
}

.primary-keyword-detailed {
    border: 1px solid rgba(37, 99, 235, 0.2);
    border-radius: 8px;
    padding: 15px;
    background: rgba(37, 99, 235, 0.02);
}

.keyword-header h6 {
    color: var(--primary-color);
    margin: 8px 0;
    font-size: 1.1rem;
}

.secondary-keywords-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.secondary-keyword-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
}

.secondary-keyword-item .keyword-name {
    display: block;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.secondary-keyword-item .keyword-count,
.secondary-keyword-item .keyword-density {
    display: inline-block;
    font-size: 0.85rem;
    color: #6b7280;
    margin: 0 5px;
}

.improvements-breakdown {
    background: white;
    border-radius: 8px;
    padding: 15px;
}

.improvement-category {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 15px;
}

.improvement-category:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.improvement-category h6 {
    color: var(--dark-color);
    margin-bottom: 10px;
    font-size: 1rem;
}

.improvement-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.improvement-list li {
    padding: 5px 0;
    padding-right: 20px;
    position: relative;
    font-size: 0.9rem;
    line-height: 1.5;
}

.improvement-list li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
}

.recommendations-list {
    background: white;
    border-radius: 8px;
    padding: 15px;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    padding: 8px 0;
    border-bottom: 1px solid rgba(249, 115, 22, 0.1);
    font-size: 0.9rem;
    line-height: 1.5;
}

.recommendation-item:last-child {
    border-bottom: none;
}

.original-content-display {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.original-text {
    background: white;
    border-radius: 6px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    max-height: 60vh;
    overflow-y: auto;
}

/* تحسينات التبويبات */
.nav-tabs .nav-link {
    color: #6b7280;
    border: none;
    border-bottom: 2px solid transparent;
    background: none;
    padding: 10px 15px;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: none;
}

.nav-tabs .nav-link:hover {
    color: var(--primary-color);
    border-bottom-color: rgba(37, 99, 235, 0.3);
}

/* تحسينات النافذة المنبثقة */
.modal-xl {
    max-width: 90%;
}

.modal-body {
    max-height: 80vh;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
        margin: 10px;
    }

    .stats-comparison .row {
        margin: 0;
    }

    .stats-comparison .col-md-6 {
        padding: 5px;
    }

    .secondary-keywords-grid {
        grid-template-columns: 1fr;
    }
}
